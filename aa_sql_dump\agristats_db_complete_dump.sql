﻿-- =====================================================
-- AGRISTATS_DB DATABASE DUMP
-- Generated on: 2025-08-09
-- =====================================================
-- 
-- NOTE: This database dump was created from a MySQL data directory
-- where the InnoDB engine was not recognizing the table files.
-- The .frm (structure) and .ibd (data) files exist but are not
-- accessible through the MySQL engine.
-- 
-- This dump includes:
-- 1. Database creation statements
-- 2. Table structure placeholders for all identified tables
-- 3. Comments about data recovery requirements
-- 
-- RECOVERY INSTRUCTIONS:
-- To recover the actual table data, you may need to:
-- 1. Restore the ibdata1-B file to ibdata1
-- 2. Restart MySQL service
-- 3. Run REPAIR TABLE commands on each table
-- 4. Re-export using mysqldump once tables are accessible
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `agristats_db`;
CREATE DATABASE `agristats_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `agristats_db`;

-- =====================================================
-- IDENTIFIED TABLES FROM FILE SYSTEM
-- =====================================================
-- The following 45 tables were found in the agristats_db directory:
-- Each table has corresponding .frm (structure) and .ibd (data) files

-- Administrative/Reference Tables:
-- - adx_country (Country reference data)
-- - adx_province (Province reference data)
-- - adx_district (District reference data)
-- - adx_llg (Local Level Government reference data)
-- - adx_ward (Ward reference data)
-- - adx_education (Education levels reference data)

-- Agricultural Reference Tables:
-- - adx_crops (Crop types and varieties)
-- - adx_livestock (Livestock types)
-- - adx_fertilizers (Fertilizer types and compositions)
-- - adx_pesticides (Pesticide types and active ingredients)
-- - adx_infections (Disease and infection types)

-- User and Organization Management:
-- - dakoii_users (System users)
-- - dakoii_org (Organization data)
-- - users (Application users)

-- Farmer and Farm Management:
-- - farmer_information (Farmer personal data)
-- - farmers_children (Farmer family information)
-- - crops_farm_blocks (Farm block definitions for crops)
-- - crops_farm_block_files (Files associated with crop farm blocks)
-- - livestock_farm_blocks (Farm block definitions for livestock)
-- - livestock_farm_data (Livestock farming data)
-- - livestock_production_data (Livestock production records)

-- Crop Production and Management:
-- - crops_farm_crops_data (Crop production data)
-- - crops_farm_disease_data (Crop disease records)
-- - crops_farm_fertilizer_data (Fertilizer usage data)
-- - crops_farm_harvest_data (Harvest records)
-- - crops_farm_marketing_data (Crop marketing information)
-- - crops_farm_pesticides_data (Pesticide usage data)
-- - crops_farm_tools (Farm tools and equipment)

-- Business and Market Data:
-- - crop_buyers (Crop buyer information)
-- - crop_processors (Crop processing facilities)
-- - inputs (Agricultural input suppliers)

-- Administrative and Operational:
-- - climate_focus (Climate-related focus areas)
-- - field_visits (Field visit records)
-- - gov_structure (Government structure data)
-- - groupings (Various grouping classifications)
-- - exercises (Training exercises or surveys)
-- - exercise_officers (Officers assigned to exercises)
-- - trainings (Training programs and sessions)
-- - workplan_infrastructure_activities (Infrastructure work plans)

-- Document Management:
-- - documents_folder (Document folder structure)
-- - document_files (Document file records)

-- Security and Permissions:
-- - permissions_items (Permission items)
-- - permissions_sets (Permission sets)
-- - permissions_user_districts (User district permissions)

-- System Configuration:
-- - settings (System settings and configurations)

-- =====================================================
-- BASIC TABLE STRUCTURES (Estimated)
-- =====================================================
-- Note: These are estimated structures based on common patterns
-- Actual structures should be recovered from .frm files

-- Administrative Reference Tables
CREATE TABLE `adx_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_province` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_province_country` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_district` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_district_province` (`province_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_llg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_llg_district` (`district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_ward` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_ward_llg` (`llg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agricultural Reference Tables
CREATE TABLE `adx_crops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `scientific_name` varchar(255) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_livestock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_fertilizers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `composition` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_pesticides` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `active_ingredient` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_infections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `adx_education` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Management Tables
CREATE TABLE `dakoii_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `dakoii_org` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PLACEHOLDER TABLES FOR REMAINING STRUCTURES
-- =====================================================
-- The following tables exist but need structure recovery:

-- Farmer Management
-- CREATE TABLE `farmer_information` (...);
-- CREATE TABLE `farmers_children` (...);

-- Farm Block Management
-- CREATE TABLE `crops_farm_blocks` (...);
-- CREATE TABLE `crops_farm_block_files` (...);
-- CREATE TABLE `livestock_farm_blocks` (...);

-- Production Data
-- CREATE TABLE `crops_farm_crops_data` (...);
-- CREATE TABLE `crops_farm_disease_data` (...);
-- CREATE TABLE `crops_farm_fertilizer_data` (...);
-- CREATE TABLE `crops_farm_harvest_data` (...);
-- CREATE TABLE `crops_farm_marketing_data` (...);
-- CREATE TABLE `crops_farm_pesticides_data` (...);
-- CREATE TABLE `crops_farm_tools` (...);
-- CREATE TABLE `livestock_farm_data` (...);
-- CREATE TABLE `livestock_production_data` (...);

-- Business and Market
-- CREATE TABLE `crop_buyers` (...);
-- CREATE TABLE `crop_processors` (...);
-- CREATE TABLE `inputs` (...);

-- Administrative
-- CREATE TABLE `climate_focus` (...);
-- CREATE TABLE `field_visits` (...);
-- CREATE TABLE `gov_structure` (...);
-- CREATE TABLE `groupings` (...);
-- CREATE TABLE `exercises` (...);
-- CREATE TABLE `exercise_officers` (...);
-- CREATE TABLE `trainings` (...);
-- CREATE TABLE `workplan_infrastructure_activities` (...);

-- Document Management
-- CREATE TABLE `documents_folder` (...);
-- CREATE TABLE `document_files` (...);

-- Security and Permissions
-- CREATE TABLE `permissions_items` (...);
-- CREATE TABLE `permissions_sets` (...);
-- CREATE TABLE `permissions_user_districts` (...);

-- System
-- CREATE TABLE `settings` (...);

-- =====================================================
-- DATA RECOVERY INSTRUCTIONS
-- =====================================================
-- 
-- CRITICAL: The actual table data is stored in .ibd files but is not
-- accessible due to InnoDB engine issues. To recover the data:
-- 
-- 1. BACKUP CURRENT STATE:
--    - Copy the entire agristats_db folder
--    - Backup current ibdata1 file
-- 
-- 2. RESTORE IBDATA1:
--    - Stop MySQL service
--    - Replace ibdata1 with ibdata1-B
--    - Start MySQL service
-- 
-- 3. REPAIR TABLES:
--    - Run: REPAIR TABLE table_name; for each table
--    - Or: mysqlcheck -u root --repair agristats_db
-- 
-- 4. VERIFY DATA ACCESS:
--    - Test: SELECT COUNT(*) FROM table_name; for each table
-- 
-- 5. CREATE PROPER DUMP:
--    - Run: mysqldump -u root --single-transaction --routines --triggers agristats_db > agristats_db_with_data.sql
-- 
-- TABLE FILES SUMMARY:
-- - Total tables identified: 45
-- - All have .frm (structure) files
-- - All have .ibd (data) files
-- - Database size: Approximately [check .ibd file sizes]
-- 
-- =====================================================
-- EXTRACTED DATA FROM .IBD FILES
-- =====================================================

-- Country data extracted from adx_country.ibd
INSERT INTO `adx_country` (`id`, `name`, `code`) VALUES (1, 'Papua New Guinea', 'PG');
INSERT INTO `adx_country` (`id`, `name`, `code`) VALUES (2, 'Australia', 'AU');

-- Crop data extracted from adx_crops.ibd
INSERT INTO `adx_crops` (`id`, `name`) VALUES (1, 'Cocoa');
INSERT INTO `adx_crops` (`id`, `name`) VALUES (2, 'Coffee');
INSERT INTO `adx_crops` (`id`, `name`) VALUES (3, 'Rubber');

-- User data extracted from users.ibd (emails only)
INSERT INTO `users` (`id`, `username`, `email`) VALUES (1, 'DASatesta', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (2, 'Forllotesta', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (3, 'Flajoaitapeitu', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (4, 'Boxxanziinols', '<EMAIL>');

-- =====================================================
-- DATA EXTRACTION SUMMARY
-- =====================================================
--
-- Successfully extracted data from the following .ibd files:
-- 1. adx_country.ibd - 2 countries (Papua New Guinea, Australia)
-- 2. adx_crops.ibd - 3 crops (Cocoa, Coffee, Rubber)
-- 3. users.ibd - 4 user email addresses
--
-- EXTRACTION METHOD:
-- Used binary file parsing with regex pattern matching to extract
-- readable text data from InnoDB tablespace files (.ibd).
--
-- LIMITATIONS:
-- - Only readable text data could be extracted
-- - Binary data, encrypted data, and complex structures are not included
-- - IDs may not match original database auto-increment values
-- - Foreign key relationships are not preserved
-- - Timestamps and other metadata are missing
-- - Many tables may contain additional data not extractable via text patterns
--
-- ADDITIONAL DATA SOURCES:
-- The following .ibd files exist and may contain more data:
-- - adx_province.ibd, adx_district.ibd, adx_llg.ibd, adx_ward.ibd
-- - adx_fertilizers.ibd, adx_pesticides.ibd, adx_infections.ibd
-- - farmer_information.ibd, crops_farm_*.ibd, livestock_*.ibd
-- - And 30+ other table files
--
-- For complete data recovery, restore ibdata1-B and use proper MySQL recovery.
--
-- =====================================================
-- END OF DUMP
-- =====================================================
