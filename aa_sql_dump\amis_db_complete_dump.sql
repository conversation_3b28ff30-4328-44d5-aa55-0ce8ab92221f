-- =====================================================
-- AMIS_DB DATABASE DUMP
-- Generated on: 2025-08-09
-- =====================================================
-- 
-- NOTE: This database dump was created from a MySQL data directory
-- where the InnoDB engine was not recognizing the table files.
-- The .frm (structure) and .ibd (data) files exist but are not
-- accessible through the MySQL engine.
-- 
-- This dump includes:
-- 1. Database creation statements
-- 2. Table structure placeholders for all identified tables
-- 3. Comments about data recovery requirements
-- 
-- RECOVERY INSTRUCTIONS:
-- To recover the actual table data, you may need to:
-- 1. Restore the ibdata1-B file to ibdata1
-- 2. Restart MySQL service
-- 3. Run REPAIR TABLE commands on each table
-- 4. Re-export using mysqldump once tables are accessible
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `amis_db`;
CREATE DATABASE `amis_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `amis_db`;

-- =====================================================
-- IDENTIFIED TABLES FROM FILE SYSTEM
-- =====================================================
-- The following 39 tables were found in the amis_db directory:
-- Each table has corresponding .frm (structure) and .ibd (data) files

-- Administrative/Reference Tables:
-- - adx_country (Country reference data)
-- - gov_structure (Government structure data)
-- - regions (Regional divisions)
-- - region_province_link (Region-Province relationships)

-- User and Organization Management:
-- - dakoii_users (System users)
-- - users (Application users)
-- - branches (Organization branches)
-- - org_settings (Organization settings)

-- Small and Medium Enterprises (SME):
-- - sme (SME business information)
-- - sme_staff (SME staff records)

-- Document Management:
-- - documents (Document records)
-- - folders (Document folder structure)

-- Commodities and Market Data:
-- - commodities (Commodity types and specifications)
-- - commodity_prices (Price tracking data)
-- - commodity_production (Production statistics)

-- Meeting and Agreement Management:
-- - meetings (Meeting records and scheduling)
-- - agreements (Business and policy agreements)
-- - proposal (Project and business proposals)

-- Planning and Development:
-- - plans_corporate_plan (Corporate strategic plans)
-- - plans_mtdp (Medium Term Development Plans)
-- - plans_mtdp_dip (MTDP District Implementation Plans)
-- - plans_mtdp_indicators (MTDP performance indicators)
-- - plans_mtdp_investments (MTDP investment allocations)
-- - plans_mtdp_kra (MTDP Key Result Areas)
-- - plans_mtdp_spa (MTDP Strategic Priority Areas)
-- - plans_mtdp_specific_area (MTDP specific focus areas)
-- - plans_mtdp_strategies (MTDP implementation strategies)
-- - plans_nasp (National Agriculture Sector Plans)

-- Workplan Management:
-- - workplans (Master workplan records)
-- - workplan_activities (General workplan activities)
-- - workplan_corporate_plan_link (Links to corporate plans)
-- - workplan_infrastructure_activities (Infrastructure-related activities)
-- - workplan_input_activities (Input supply activities)
-- - workplan_mtdp_link (Links to MTDP plans)
-- - workplan_nasp_link (Links to NASP)
-- - workplan_others_link (Links to other plans)
-- - workplan_output_activities (Output-focused activities)
-- - workplan_training_activities (Training and capacity building)

-- System Management:
-- - migrations (Database migration history)

-- =====================================================
-- BASIC TABLE STRUCTURES (Estimated)
-- =====================================================
-- Note: These are estimated structures based on common patterns
-- Actual structures should be recovered from .frm files

-- Administrative Reference Tables
CREATE TABLE `adx_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `gov_structure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `level` varchar(50) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_gov_structure_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `regions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `region_province_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_region_province_region` (`region_id`),
  KEY `fk_region_province_province` (`province_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Management Tables
CREATE TABLE `dakoii_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_branches_manager` (`manager_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `org_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- SME Management Tables
CREATE TABLE `sme` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(255) NOT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `business_type` varchar(100) DEFAULT NULL,
  `owner_name` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sme_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sme_id` int(11) NOT NULL,
  `staff_name` varchar(255) NOT NULL,
  `position` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `status` enum('active','inactive','terminated') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_sme_staff_sme` (`sme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Document Management Tables
CREATE TABLE `folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `path` varchar(500) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_folders_parent` (`parent_id`),
  KEY `fk_folders_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `folder_id` int(11) DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `status` enum('active','archived','deleted') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_documents_folder` (`folder_id`),
  KEY `fk_documents_uploaded_by` (`uploaded_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Commodity Management Tables
CREATE TABLE `commodities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `commodity_prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commodity_id` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `market_location` varchar(255) DEFAULT NULL,
  `price_date` date NOT NULL,
  `source` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_commodity_prices_commodity` (`commodity_id`),
  KEY `idx_commodity_prices_date` (`price_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `commodity_production` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commodity_id` int(11) NOT NULL,
  `production_quantity` decimal(15,2) NOT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `production_period` varchar(20) DEFAULT NULL,
  `production_year` year(4) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_commodity_production_commodity` (`commodity_id`),
  KEY `idx_commodity_production_year` (`production_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Meeting and Agreement Management
CREATE TABLE `meetings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `meeting_date` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `organizer_id` int(11) DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_meetings_organizer` (`organizer_id`),
  KEY `idx_meetings_date` (`meeting_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `agreements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `agreement_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('draft','active','expired','terminated') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_agreements_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `proposal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `proposal_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `budget_amount` decimal(15,2) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `submission_date` date DEFAULT NULL,
  `status` enum('draft','submitted','under_review','approved','rejected') DEFAULT 'draft',
  `submitted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_proposal_submitted_by` (`submitted_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Management
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PLACEHOLDER TABLES FOR REMAINING STRUCTURES
-- =====================================================
-- The following tables exist but need structure recovery:

-- Planning Tables (Complex structures requiring .frm analysis):
-- CREATE TABLE `plans_corporate_plan` (...);
-- CREATE TABLE `plans_mtdp` (...);
-- CREATE TABLE `plans_mtdp_dip` (...);
-- CREATE TABLE `plans_mtdp_indicators` (...);
-- CREATE TABLE `plans_mtdp_investments` (...);
-- CREATE TABLE `plans_mtdp_kra` (...);
-- CREATE TABLE `plans_mtdp_spa` (...);
-- CREATE TABLE `plans_mtdp_specific_area` (...);
-- CREATE TABLE `plans_mtdp_strategies` (...);
-- CREATE TABLE `plans_nasp` (...);

-- Workplan Tables:
-- CREATE TABLE `workplans` (...);
-- CREATE TABLE `workplan_activities` (...);
-- CREATE TABLE `workplan_corporate_plan_link` (...);
-- CREATE TABLE `workplan_infrastructure_activities` (...);
-- CREATE TABLE `workplan_input_activities` (...);
-- CREATE TABLE `workplan_mtdp_link` (...);
-- CREATE TABLE `workplan_nasp_link` (...);
-- CREATE TABLE `workplan_others_link` (...);
-- CREATE TABLE `workplan_output_activities` (...);
-- CREATE TABLE `workplan_training_activities` (...);

-- =====================================================
-- DATA RECOVERY INSTRUCTIONS
-- =====================================================
-- 
-- CRITICAL: The actual table data is stored in .ibd files but is not
-- accessible due to InnoDB engine issues. To recover the data:
-- 
-- 1. BACKUP CURRENT STATE:
--    - Copy the entire amis_db folder
--    - Backup current ibdata1 file
-- 
-- 2. RESTORE IBDATA1:
--    - Stop MySQL service
--    - Replace ibdata1 with ibdata1-B
--    - Start MySQL service
-- 
-- 3. REPAIR TABLES:
--    - Run: REPAIR TABLE table_name; for each table
--    - Or: mysqlcheck -u root --repair amis_db
-- 
-- 4. VERIFY DATA ACCESS:
--    - Test: SELECT COUNT(*) FROM table_name; for each table
-- 
-- 5. CREATE PROPER DUMP:
--    - Run: mysqldump -u root --single-transaction --routines --triggers amis_db > amis_db_with_data.sql
-- 
-- TABLE FILES SUMMARY:
-- - Total tables identified: 39
-- - All have .frm (structure) files
-- - All have .ibd (data) files
-- - Database focus: Agriculture Market Information System
-- - Primary modules: SME Management, Commodity Trading, Planning, Workplan Management
-- 
-- =====================================================
-- END OF DUMP
-- =====================================================