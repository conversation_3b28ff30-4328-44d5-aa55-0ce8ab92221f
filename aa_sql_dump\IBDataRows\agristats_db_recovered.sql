-- Best-effort recovery from ibdata1: table names and nearby text snippets
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS=0;
DROP DATABASE IF EXISTS `agristats_db_recovered`;
CREATE DATABASE `agristats_db_recovered` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `agristats_db_recovered`;
CREATE TABLE `__recovery_notes` ( `note_id` INT AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(190), `source_file` VARCHAR(255), `window_bytes` INT, `max_rows` INT, `comment` TEXT);
INSERT INTO `__recovery_notes` (`table_name`, `source_file`, `window_bytes`, `max_rows`, `comment`) VALUES ('ALL', 'ibdata1-B', 8192, 50, 'Heuristic neighbor-string extraction around table name occurrences; schema unknown; values are untyped text snippets.');
DROP TABLE IF EXISTS `_sql`;
CREATE TABLE `_sql` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `_sql` (`context_offset`, `raw_text`) VALUES
(1556772, 'amis_db'),
(1556780, 'plans_mtdp_specific_area'),
(1557922, 'amis_db/#sql-ib4467'),
(1558236, 'amis_db/#sql-517c_134'),
(1558879, '#mysql50##sql-517c_134'),
(1558952, 'plans_corporate_plan'),
(1559412, 'proposal'),
(1559733, 'workplan_training_activities'),
(1559762, 'PRIMARY'),
(1559770, 'n_diff_pfx01'),
(1559862, '#mysql50##sql2-517c-134'),
(1559973, 'n_leaf_pages'),
(1561600, 'pcollx_db'),
(1562387, 'wptest_db'),
(1562397, 'wp_term_relationships'),
(1562487, 'n_diff_pfx02'),
(1562651, 'term_taxonomy_id'),
(1564507, '1754345328:1'),
(1565437, 'agristats_db'),
(1565450, 'crops_farm_disease_data'),
(1565491, 'crops_farm_disease_data"'),
(1565535, '#mysql50##sql2-fe98-562#L'),
(1565774, '144.151979'),
(1565786, '-4.048756'),
(1566033, '_transient_doing_cron'),
(1566265, 'adx_llg&&'),
(1623288, 'org_settings'),
(1623494, 'amis_db"workplan_infrastructure_activities'),
(1623752, 'folders'),
(1623981, 'workplan_input_activities'),
(1624634, 'workplan_activities'),
(1626180, 'geo_provinces'),
(1626408, '<$2y$10$9HqdpK6943ZgyaGPNqSsb.rNpR2/oT0Q2aahUXxqin8o0x9uGKiDy'),
(1626673, 'wp_usermeta'),
(1626827, 'meta_key'),
(1627023, 'user_id'),
(1627609, 'a:4:{s:19:"wp_inactive_widgets";a:0:{}s:9:"sidebar-1";a:5:{i:0;s:7:"block-2";i:1;s:7:"block-3";i:2;s:7:"block-4";i:3;s:7:"block-5";i:4;s:7:"block-6";}s:8:"footer-1";a:0:{}s:13:"array_version";i:3;}'),
(1628026, '1754345529:1'),
(1628544, '.\\agristats_db\\#sql-fe98_562.ibd'),
(1628808, '_site_transient_update_plugins'),
(1629253, 'adx_pesticides'),
(1818928, 'publish'),
(1818965, '$a7cfe3d4-e8f9-4adc-af6d-fbad158629f8'),
(1819014, 'customize_changeset'),
(1819450, '.\\agristats_db\\#sql-fe98_2f4.ibd'),
(1819704, '#mysql50##sql2-fe98-562'),
(1823886, 'adx_ward'),
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district');
DROP TABLE IF EXISTS `_sql2`;
CREATE TABLE `_sql2` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `_sql2` (`context_offset`, `raw_text`) VALUES
(1787743, 'n_diff_pfx01'),
(1787777, 'amis_db'),
(1787785, 'plans_mtdp_strategies'),
(1787807, 'GEN_CLUST_INDEX'),
(1787823, 'n_leaf_pages'),
(1788237, 'amis_db/workplan_mtdp_link'),
(1788311, 'amis_db/#sql-ib4468'),
(1788352, 'amis_db/#sql-517c_134'),
(1788586, '.\\amis_db\\workplan_mtdp_link.ibd'),
(1788698, '.\\amis_db\\#sql-517c_134.ibd'),
(1788941, 'commodity_production'),
(1788962, 'PRIMARY'),
(1789054, '#mysql50##sql2-517c-134'),
(1789650, '#mysql50##sql-517c_134'),
(1790019, 'plans_nasp'),
(1790424, 'workplan_activities'),
(1791008, 'ders_db'),
(1791016, 'appx_application_experiences'),
(1791238, 'agristats_db'),
(1791251, 'adx_crops'),
(1791476, '[{"db":"agristats_db","table":"users"},{"db":"agristats_db","table":"permissions_user_districts"},{"db":"agristats_db","table":"permissions_sets"},{"db":"agristats_db","table":"permissions_items"}]'),
(1791867, 'pcollx_db'),
(1791877, 'dakoii_org'),
(1792216, 'pcollx_db/#sql-96a8_232'),
(1792784, 'pcollx_db/geo_districts'),
(1793708, 'charity-zone'),
(1794204, 'n_diff_pfx01 ?'),
(1794237, '#mysql50##sql2-fe98-2f4'),
(1794441, 'n_leaf_pages!V!'),
(1796679, 'adx_province'),
(1796700, 'n_diff_pfx01*4*'),
(1796754, 'n_leaf_pages*j*'),
(1917880, 'post_author'),
(1917908, 'wptest_db'),
(1917919, 'wp_posts'),
(1918129, 'n_diff_pfx02'),
(1918806, 'post_name'),
(1919710, 'post_parent'),
(1920646, 'type_status_date'),
(1921197, 'n_diff_pfx03'),
(1921464, 'n_diff_pfx04'),
(1922385, 'H_site_transient_wp_theme_files_patterns-32b2b514fa7371f374420f5d2237e73a'),
(1926650, 'H_site_transient_wp_theme_files_patterns-23add107ce69b15ad654964e07dd24aa'),
(1927138, 'climate_focus'),
(1927160, 'n_diff_pfx01'''),
(1927215, 'n_leaf_pages('),
(1927270, 'size(='),
(2605977, 'plans_mtdp_indicators'),
(2607003, 'amis_db/workplan_activities'),
(2607613, 'appx_application_education');
DROP TABLE IF EXISTS `adx_country`;
CREATE TABLE `adx_country` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_country` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_crops`;
CREATE TABLE `adx_crops` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_crops` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_crops_`;
CREATE TABLE `adx_crops_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_crops_` (`context_offset`, `raw_text`) VALUES
(5767165, '>#"HNK'),
(5767269, 'infimum'),
(5767284, 'supremum'),
(5767309, 'sendme_db/adx_country'),
(5767346, 'sendme_db/adx_district'),
(5767384, 'sendme_db/adx_llg'),
(5767417, 'sendme_db/adx_province'),
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward');
DROP TABLE IF EXISTS `adx_district`;
CREATE TABLE `adx_district` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_district` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_education`;
CREATE TABLE `adx_education` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_education` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_education_`;
CREATE TABLE `adx_education_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_education_` (`context_offset`, `raw_text`) VALUES
(5767165, '>#"HNK'),
(5767269, 'infimum'),
(5767284, 'supremum'),
(5767309, 'sendme_db/adx_country'),
(5767346, 'sendme_db/adx_district'),
(5767384, 'sendme_db/adx_llg'),
(5767417, 'sendme_db/adx_province'),
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward');
DROP TABLE IF EXISTS `adx_fertilizers`;
CREATE TABLE `adx_fertilizers` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_fertilizers` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_fertilizers_`;
CREATE TABLE `adx_fertilizers_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_fertilizers_` (`context_offset`, `raw_text`) VALUES
(5767165, '>#"HNK'),
(5767269, 'infimum'),
(5767284, 'supremum'),
(5767309, 'sendme_db/adx_country'),
(5767346, 'sendme_db/adx_district'),
(5767384, 'sendme_db/adx_llg'),
(5767417, 'sendme_db/adx_province'),
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward');
DROP TABLE IF EXISTS `adx_infections`;
CREATE TABLE `adx_infections` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_infections` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_livestock`;
CREATE TABLE `adx_livestock` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_livestock` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_llg`;
CREATE TABLE `adx_llg` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_llg` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_pesticides`;
CREATE TABLE `adx_pesticides` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_pesticides` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_province`;
CREATE TABLE `adx_province` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_province` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `adx_ward`;
CREATE TABLE `adx_ward` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `adx_ward` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `climate_focus`;
CREATE TABLE `climate_focus` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `climate_focus` (`context_offset`, `raw_text`) VALUES
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings'),
(5202657, 'grass_db/positions'),
(5202707, 'grass_db/exercises'),
(5202757, 'dipia_db/users');
DROP TABLE IF EXISTS `crop_buyers`;
CREATE TABLE `crop_buyers` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crop_buyers` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crop_processors`;
CREATE TABLE `crop_processors` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crop_processors` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_block_files`;
CREATE TABLE `crops_farm_block_files` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_block_files` (`context_offset`, `raw_text`) VALUES
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups'),
(5196467, 'payrollwan_db/payrolls'),
(5196521, 'payrollwan_db/payslips'),
(5196575, 'payrollwan_db/pay_period'),
(5196631, 'payrollwan_db/positions'),
(5196686, 'payrollwan_db/positions_groups'),
(5196748, 'payrollwan_db/selection'),
(5196803, 'payrollwan_db/settings'),
(5196857, 'payrollwan_db/users'),
(5196908, 'selmasta_db/applicants'),
(5196962, 'hrwan_db/employee_files'),
(5197017, 'hrwan_db/groups'),
(5197064, 'hrwan_db/payrolls'),
(5197113, 'hrwan_db/payslips'),
(5197162, 'hrwan_db/pay_period'),
(5197213, 'hrwan_db/positions'),
(5197263, 'hrwan_db/positions_groups'),
(5197320, 'hrwan_db/earnings_deductions'),
(5197380, 'hrwan_db/employees'),
(5197430, 'hrwan_db/emp_leave'),
(5197480, 'hrwan_db/migrations'),
(5197531, 'hrwan_db/employment_logs'),
(5197587, 'dipia_db/stakeholders_reports_details'),
(5197656, 'dipia_db/stakeholders_reports_header'),
(5198981, 'dipia_db/commodity_boards'),
(5199038, 'dipia_db/crops'),
(5199084, 'dipia_db/production_data_sme'),
(5199144, 'dipia_db/production_data_commodity_boards');
DROP TABLE IF EXISTS `crops_farm_block_files_`;
CREATE TABLE `crops_farm_block_files_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_block_files_` (`context_offset`, `raw_text`) VALUES
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward'),
(5769007, '1part_db/dakoii_org'),
(5769041, '4part_db/dakoii_users'),
(5769077, 'Ipart_db/selection'),
(5769110, 'Lpart_db/settings'),
(5769142, '[part_db/users'),
(5769172, 'part_db/assess_items#'),
(5769208, 'part_db/assess_items_groups');
DROP TABLE IF EXISTS `crops_farm_blocks`;
CREATE TABLE `crops_farm_blocks` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_blocks` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_crops_data`;
CREATE TABLE `crops_farm_crops_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_crops_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_disease_data`;
CREATE TABLE `crops_farm_disease_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_disease_data` (`context_offset`, `raw_text`) VALUES
(1310998, 'amis_db'),
(1311006, '#mysql50##sql-517c_134'),
(1311029, 'PRIMARY'),
(1311037, 'n_diff_pfx01'),
(1311096, 'n_leaf_pages'),
(1311375, 'plans_mtdp_strategies'),
(1311446, '#mysql50##sql2-517c-134'),
(1311802, 'workplans'),
(1312356, 'amis_db/#sql-517c_134'),
(1312998, 'agristats_db"workplan_infrastructure_activities'),
(1313637, 'pcollx_db'),
(1313647, '#mysql50##sql-96a8_21e'),
(1313738, 'geo_countries'),
(1314193, 'wptest_db'),
(1314203, 'wp_postmeta'),
(1314357, 'meta_key'),
(1314417, 'n_diff_pfx02'),
(1314553, 'post_id'),
(1315138, 'espa_wp_theme'),
(1315371, 'publish'),
(1315408, '$97d23f26-d34e-418b-82db-51be0d27cce5'),
(1315457, 'customize_changeset'),
(1316707, 'exercise_id'),
(1316780, 'block_id'),
(1316844, 'crop_id'),
(1316925, 'disease_type'),
(1316993, 'disease_name'),
(1317079, 'description'),
(1317146, 'action_reason'),
(1317233, 'number_of_plants'),
(1317402, 'action_date'),
(1317475, 'hectares'),
(1317557, 'remarks'),
(1317638, 'created_by'),
(1317722, 'created_at'),
(1317806, 'updated_by'),
(1317890, 'updated_at'),
(1317974, 'status'),
(1318054, 'deleted_at'),
(1320824, 'agristats_db'),
(1320837, 'adx_fertilizers''r'),
(1623288, 'org_settings'),
(1623494, 'amis_db"workplan_infrastructure_activities'),
(1623752, 'folders'),
(1623981, 'workplan_input_activities'),
(1624634, 'workplan_activities'),
(1625057, 'workplan_training_activities'),
(1626180, 'geo_provinces'),
(1626408, '<$2y$10$9HqdpK6943ZgyaGPNqSsb.rNpR2/oT0Q2aahUXxqin8o0x9uGKiDy'),
(1626673, 'wp_usermeta');
DROP TABLE IF EXISTS `crops_farm_fertilizer_data`;
CREATE TABLE `crops_farm_fertilizer_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_fertilizer_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_harvest_data`;
CREATE TABLE `crops_farm_harvest_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_harvest_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_marketing_data`;
CREATE TABLE `crops_farm_marketing_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_marketing_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_marketing_data_`;
CREATE TABLE `crops_farm_marketing_data_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_marketing_data_` (`context_offset`, `raw_text`) VALUES
(5767165, '>#"HNK'),
(5767269, 'infimum'),
(5767284, 'supremum'),
(5767309, 'sendme_db/adx_country'),
(5767346, 'sendme_db/adx_district'),
(5767384, 'sendme_db/adx_llg'),
(5767417, 'sendme_db/adx_province'),
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward');
DROP TABLE IF EXISTS `crops_farm_pesticides_data`;
CREATE TABLE `crops_farm_pesticides_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_pesticides_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `crops_farm_tools`;
CREATE TABLE `crops_farm_tools` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `crops_farm_tools` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `dakoii_org`;
CREATE TABLE `dakoii_org` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `dakoii_org` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `dakoii_users`;
CREATE TABLE `dakoii_users` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `dakoii_users` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `document_files`;
CREATE TABLE `document_files` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `document_files` (`context_offset`, `raw_text`) VALUES
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings'),
(5202657, 'grass_db/positions'),
(5202707, 'grass_db/exercises');
DROP TABLE IF EXISTS `documents_folder`;
CREATE TABLE `documents_folder` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `documents_folder` (`context_offset`, `raw_text`) VALUES
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings'),
(5202657, 'grass_db/positions');
DROP TABLE IF EXISTS `documents_folder_`;
CREATE TABLE `documents_folder_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `documents_folder_` (`context_offset`, `raw_text`) VALUES
(5770372, '`dipia_db/act_proposals%'),
(5770410, 'bdipia_db/act_training_details'),
(5770455, 'ddipia_db/org_branches'),
(5770492, 'fdipia_db/adx_ward'),
(5770525, 'kdipia_db/act_agreements'),
(5770565, 'selmasta_db/dakoii_org'),
(5770603, 'selmasta_db/dakoii_users'),
(5770643, 'selmasta_db/selection'),
(5770680, 'selmasta_db/users'),
(5770713, 'selsys_db/users$'),
(5770744, 'selmasta_db/positions_groups'),
(5770788, 'selmasta_db/positions!'),
(5770825, 'payrollwan_db/adx_country"'),
(5770866, 'payrollwan_db/adx_district'),
(5770908, 'payrollwan_db/adx_llg"'),
(5770945, 'payrollwan_db/adx_province'),
(5770987, 'payrollwan_db/adx_ward'),
(5771025, 'payrollwan_db/dakoii_org"'),
(5771065, 'payrollwan_db/dakoii_users)'),
(5771107, 'payrollwan_db/earnings_deductions'),
(5771156, 'payrollwan_db/employees'),
(5771195, 'payrollwan_db/groups'),
(5771231, 'payrollwan_db/payrolls'),
(5771268, '"payrollwan_db/payslips'),
(5771306, '%payrollwan_db/pay_period'),
(5771346, '(payrollwan_db/positions&'),
(5771385, '+payrollwan_db/positions_groups'),
(5771431, '.payrollwan_db/selection'),
(5771470, '1payrollwan_db/settings'),
(5771508, '4payrollwan_db/users'),
(5771543, '<selmasta_db/applicants'),
(5771581, 'Ahrwan_db/employee_files'),
(5771620, 'Bhrwan_db/groups'),
(5771651, 'Chrwan_db/payrolls'),
(5771684, 'Dhrwan_db/payslips'),
(5771717, 'Ehrwan_db/pay_period'),
(5771752, 'Fhrwan_db/positions!'),
(5771786, 'Ghrwan_db/positions_groups$'),
(5771827, 'Hhrwan_db/earnings_deductions'),
(5771871, 'Ihrwan_db/employees'),
(5771905, 'Khrwan_db/emp_leave'),
(5771939, 'Lhrwan_db/migrations'),
(5771974, 'Mhrwan_db/employment_logs-'),
(5772014, 'Ndipia_db/stakeholders_reports_details,'),
(5772067, 'Odipia_db/stakeholders_reports_header'),
(5773003, 'dipia_db/commodity_boards'),
(5773044, 'dipia_db/crops$'),
(5773074, 'dipia_db/production_data_sme1'),
(5773118, 'dipia_db/production_data_commodity_boards*'),
(5773175, 'dipia_db/production_data_provinces');
DROP TABLE IF EXISTS `exercise_officers`;
CREATE TABLE `exercise_officers` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `exercise_officers` (`context_offset`, `raw_text`) VALUES
(5198981, 'dipia_db/commodity_boards'),
(5199038, 'dipia_db/crops'),
(5199084, 'dipia_db/production_data_sme'),
(5199144, 'dipia_db/production_data_commodity_boards'),
(5199217, 'dipia_db/production_data_provinces'),
(5199283, 'dipia_db/act_training'),
(5199626, 'dipia_db/dev_nasp'),
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education');
DROP TABLE IF EXISTS `exercise_officers_exercise_id_foreign`;
CREATE TABLE `exercise_officers_exercise_id_foreign` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `exercise_officers_exercise_id_foreign` (`context_offset`, `raw_text`) VALUES
(4949846, 'flarum_db/group_user_group_id_foreign'),
(4949893, 'k)_flarum_db/group_userflarum_db/groups'),
(4949936, 'XTE1*$'),
(4949948, 'flarum_db/group_user_user_id_foreign'),
(4949994, 'k*Xflarum_db/group_userflarum_db/users'),
(4950036, 'b^O6/)'),
(4950047, 'Vflarum_db/login_providers_user_id_foreign'),
(4950101, 'Lflarum_db/login_providersflarum_db/users'),
(4950146, 'c_P92,'),
(4950158, 'flarum_db/notifications_from_user_id_foreign'),
(4950215, 'flarum_db/notificationsflarum_db/users'),
(4950257, '^ZK4-'''),
(4950268, 'flarum_db/notifications_user_id_foreign'),
(4950375, 'flarum_db/password_tokens_user_id_foreign'),
(4950429, 'flarum_db/password_tokensflarum_db/users'),
(4950473, 'VRC0)#'),
(4950484, '_flarum_db/post_user_post_id_foreign'),
(4950530, '\\!xflarum_db/post_userflarum_db/posts'),
(4950583, 'flarum_db/post_user_user_id_foreign'),
(4950628, '\\"iflarum_db/post_userflarum_db/users'),
(4950669, 'ZVA2+%'),
(4950680, '`flarum_db/posts_discussion_id_foreign'),
(4950731, 'flarum_db/postsflarum_db/discussions'),
(4950771, 'UQB3,&'),
(4950783, 'flarum_db/posts_edited_user_id_foreign'),
(4950834, 'flarum_db/postsflarum_db/users'),
(4950880, 'flarum_db/posts_hidden_user_id_foreign'),
(4950976, '7flarum_db/posts_user_id_foreign'),
(4951066, '|flarum_db/flags_post_id_foreign'),
(4951110, 'Uflarum_db/flagsflarum_db/posts'),
(4951163, '|flarum_db/flags_user_id_foreign'),
(4951207, '2flarum_db/flagsflarum_db/users'),
(4951249, 'MI;-&'),
(4951260, '>flarum_db/tags_parent_id_foreign'),
(4951306, 'flarum_db/tagsflarum_db/tags'),
(4951346, 'XTE70*'),
(4951357, '"flarum_db/tags_last_posted_user_id_foreign'),
(4951413, 'flarum_db/tagsflarum_db/users'),
(4951454, 'd`K=60'),
(4951466, 'flarum_db/tags_last_posted_discussion_id_foreign'),
(4951526, 'vflarum_db/tagsflarum_db/discussions'),
(4951574, 'RN@.''!%'),
(4951586, 'flarum_db/tag_user_tag_id_foreign'),
(4951631, 'Fflarum_db/tag_userflarum_db/tags'),
(4951672, 'TPA/("&'),
(4951684, 'flarum_db/tag_user_user_id_foreign'),
(4951730, '/flarum_db/tag_userflarum_db/users'),
(4951772, '\\XJ4-'''),
(4951784, 'flarum_db/discussion_tag_tag_id_foreign'),
(4951836, 'flarum_db/#sql-64a8_18flarum_db/tags');
DROP TABLE IF EXISTS `exercise_officersagristats_db`;
CREATE TABLE `exercise_officersagristats_db` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `exercise_officersagristats_db` (`context_offset`, `raw_text`) VALUES
(4949936, 'XTE1*$'),
(4949948, 'flarum_db/group_user_user_id_foreign'),
(4949994, 'k*Xflarum_db/group_userflarum_db/users'),
(4950036, 'b^O6/)'),
(4950047, 'Vflarum_db/login_providers_user_id_foreign'),
(4950101, 'Lflarum_db/login_providersflarum_db/users'),
(4950146, 'c_P92,'),
(4950158, 'flarum_db/notifications_from_user_id_foreign'),
(4950215, 'flarum_db/notificationsflarum_db/users'),
(4950257, '^ZK4-'''),
(4950268, 'flarum_db/notifications_user_id_foreign'),
(4950375, 'flarum_db/password_tokens_user_id_foreign'),
(4950429, 'flarum_db/password_tokensflarum_db/users'),
(4950473, 'VRC0)#'),
(4950484, '_flarum_db/post_user_post_id_foreign'),
(4950530, '\\!xflarum_db/post_userflarum_db/posts'),
(4950583, 'flarum_db/post_user_user_id_foreign'),
(4950628, '\\"iflarum_db/post_userflarum_db/users'),
(4950669, 'ZVA2+%'),
(4950680, '`flarum_db/posts_discussion_id_foreign'),
(4950731, 'flarum_db/postsflarum_db/discussions'),
(4950771, 'UQB3,&'),
(4950783, 'flarum_db/posts_edited_user_id_foreign'),
(4950834, 'flarum_db/postsflarum_db/users'),
(4950880, 'flarum_db/posts_hidden_user_id_foreign'),
(4950976, '7flarum_db/posts_user_id_foreign'),
(4951066, '|flarum_db/flags_post_id_foreign'),
(4951110, 'Uflarum_db/flagsflarum_db/posts'),
(4951163, '|flarum_db/flags_user_id_foreign'),
(4951207, '2flarum_db/flagsflarum_db/users'),
(4951249, 'MI;-&'),
(4951260, '>flarum_db/tags_parent_id_foreign'),
(4951306, 'flarum_db/tagsflarum_db/tags'),
(4951346, 'XTE70*'),
(4951357, '"flarum_db/tags_last_posted_user_id_foreign'),
(4951413, 'flarum_db/tagsflarum_db/users'),
(4951454, 'd`K=60'),
(4951466, 'flarum_db/tags_last_posted_discussion_id_foreign'),
(4951526, 'vflarum_db/tagsflarum_db/discussions'),
(4951574, 'RN@.''!%'),
(4951586, 'flarum_db/tag_user_tag_id_foreign'),
(4951631, 'Fflarum_db/tag_userflarum_db/tags'),
(4951672, 'TPA/("&'),
(4951684, 'flarum_db/tag_user_user_id_foreign'),
(4951730, '/flarum_db/tag_userflarum_db/users'),
(4951772, '\\XJ4-'''),
(4951784, 'flarum_db/discussion_tag_tag_id_foreign'),
(4951836, 'flarum_db/#sql-64a8_18flarum_db/tags'),
(4951876, 'iscussions'),
(4951890, 'lhS;4.');
DROP TABLE IF EXISTS `exercises`;
CREATE TABLE `exercises` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `exercises` (`context_offset`, `raw_text`) VALUES
(5198981, 'dipia_db/commodity_boards'),
(5199038, 'dipia_db/crops'),
(5199084, 'dipia_db/production_data_sme'),
(5199144, 'dipia_db/production_data_commodity_boards'),
(5199217, 'dipia_db/production_data_provinces'),
(5199283, 'dipia_db/act_training'),
(5199626, 'dipia_db/dev_nasp'),
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education');
DROP TABLE IF EXISTS `exercisesagristats_db`;
CREATE TABLE `exercisesagristats_db` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `exercisesagristats_db` (`context_offset`, `raw_text`) VALUES
(4980837, 'infimum'),
(4980852, 'supremum'),
(4980868, 'Lc4test_db/usersc4test_db/auth_identities_user_id_foreign='),
(4980932, '}c4test_db/usersc4test_db/auth_remember_tokens_user_id_foreign:'),
(4981002, 'c4test_db/usersc4test_db/auth_groups_users_user_id_foreign?'),
(4981068, 'c4test_db/usersc4test_db/auth_permissions_users_user_id_foreign%'),
(4981139, 'pmbx_db/bossmanpmbx_db/bossman_ibfk_16'),
(4981183, 'Xflarum_db/usersflarum_db/access_tokens_user_id_foreign1'),
(4981246, 'flarum_db/usersflarum_db/api_keys_user_id_foreignD'),
(4981303, 'flarum_db/discussionsflarum_db/discussion_user_discussion_id_foreign8'),
(4981379, 'flarum_db/usersflarum_db/discussion_user_user_id_foreign:'),
(4981443, 'flarum_db/postsflarum_db/discussions_first_post_id_foreign;'),
(4981508, 'Hflarum_db/usersflarum_db/discussions_hidden_user_id_foreign9'),
(4981575, '7flarum_db/postsflarum_db/discussions_last_post_id_foreign@'),
(4981641, 'flarum_db/usersflarum_db/discussions_last_posted_user_id_foreign4'),
(4981713, 'flarum_db/usersflarum_db/discussions_user_id_foreign5'),
(4981773, 'flarum_db/usersflarum_db/email_tokens_user_id_foreign;'),
(4981833, 'lflarum_db/groupsflarum_db/group_permission_group_id_foreign5'),
(4981901, 'flarum_db/groupsflarum_db/group_user_group_id_foreign3'),
(4981961, 'Jflarum_db/usersflarum_db/group_user_user_id_foreign8'),
(4982021, 'flarum_db/usersflarum_db/login_providers_user_id_foreign;'),
(4982085, 'flarum_db/usersflarum_db/notifications_from_user_id_foreign6'),
(4982152, 'flarum_db/usersflarum_db/notifications_user_id_foreign8'),
(4982213, 'Eflarum_db/usersflarum_db/password_tokens_user_id_foreign2'),
(4982278, 'flarum_db/postsflarum_db/post_user_post_id_foreign2'),
(4982336, 'flarum_db/usersflarum_db/post_user_user_id_foreign:'),
(4982394, 'flarum_db/discussionsflarum_db/posts_discussion_id_foreign5'),
(4982459, '6flarum_db/usersflarum_db/posts_edited_user_id_foreign5'),
(4982521, 'flarum_db/usersflarum_db/posts_hidden_user_id_foreign.'),
(4982581, '@flarum_db/usersflarum_db/posts_user_id_foreign.'),
(4982636, 'flarum_db/postsflarum_db/flags_post_id_foreign.'),
(4982690, 'flarum_db/usersflarum_db/flags_user_id_foreign.'),
(4982744, 'flarum_db/tagsflarum_db/tags_parent_id_foreign9'),
(4982798, 'flarum_db/usersflarum_db/tags_last_posted_user_id_foreignE'),
(4982863, 'flarum_db/discussionsflarum_db/tags_last_posted_discussion_id_foreign/'),
(4982940, 'flarum_db/tagsflarum_db/tag_user_tag_id_foreign1'),
(4982994, 'zflarum_db/usersflarum_db/tag_user_user_id_foreignC'),
(4983051, 'Wflarum_db/discussionsflarum_db/discussion_tag_discussion_id_foreign5'),
(4983127, 'flarum_db/tagsflarum_db/discussion_tag_tag_id_foreign:'),
(4983187, 'bflarum_db/postsflarum_db/post_mentions_tag_post_id_foreignA'),
(4983254, 'flarum_db/tagsflarum_db/post_mentions_tag_mentions_tag_id_foreign;'),
(4983327, 'flarum_db/postsflarum_db/post_mentions_post_post_id_foreignD'),
(4983394, 'flarum_db/postsflarum_db/post_mentions_post_mentions_post_id_foreign;'),
(4983470, 'flarum_db/postsflarum_db/post_mentions_user_post_id_foreignD'),
(4983537, 'flarum_db/usersflarum_db/post_mentions_user_mentions_user_id_foreign<'),
(4983613, 'flarum_db/postsflarum_db/post_mentions_group_post_id_foreignG'),
(4983680, '=flarum_db/groupsflarum_db/post_mentions_group_mentions_group_id_foreign3'),
(4983760, 'flarum_db/postsflarum_db/post_likes_post_id_foreign3'),
(4983819, 'flarum_db/usersflarum_db/post_likes_user_id_foreign%'),
(4983877, 'upart_db/bossmanpart_db/bossman_ibfk_1_1&');
DROP TABLE IF EXISTS `farmer_information`;
CREATE TABLE `farmer_information` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `farmer_information` (`context_offset`, `raw_text`) VALUES
(5216520, 'amis_db/#sql-ib4469'),
(5216578, 'amis_db/#sql-ib4470'),
(5216600, '!_link'),
(5216635, 'b"~amis_db/#sql-ib4471'),
(5216660, '!_activities'),
(5216704, 'amis_db/#sql-ib4472'),
(5216726, '!ng_activities'),
(5216772, 'amis_db/adx_country'),
(5216823, 'amis_db/agreements'),
(5216873, 'amis_db/branches'),
(5216921, 'amis_db/commodities'),
(5216972, 'amis_db/#sql-517c_134'),
(5217028, 'amis_db/dakoii_users'),
(5217140, 'amis_db/documents'),
(5217189, 'amis_db/folders'),
(5217236, 'amis_db/gov_structure'),
(5217289, 'amis_db/meetings'),
(5217337, 'amis_db/migrations'),
(5217387, 'k)wamis_db/#sql-ib4445'),
(5217441, 'amis_db/org_settings'),
(5217547, 'amis_db/plans_mtdp'),
(5217660, 'amis_db/#sql-ib4448'),
(5217817, 'Oamis_db/#sql-517c_134'),
(5218009, '!!ents'),
(5218044, 'h,Uamis_db/#sql-517c_134'),
(5218153, 'vamis_db/#sql-517c_134'),
(5218260, 'iamis_db/#sql-517c_134'),
(5218338, '!!c_area'),
(5218431, 'amis_db/plans_nasp'),
(5218545, 'amis_db/#sql-ib4456'),
(5218596, 'amis_db/proposal'),
(5218648, '3amis_db/#sql-ib4457'),
(5218700, 'amis_db/regions'),
(5218752, '`amis_db/#sql-ib4458'),
(5218857, 'amis_db/sme'),
(5218871, '!7c_134'),
(5218969, 'Gamis_db/#sql-ib4460'),
(5219021, 'amis_db/sme_staff'),
(5219074, 'amis_db/#sql-ib4461'),
(5219125, 'amis_db/users'),
(5219178, 'amis_db/#sql-ib4462'),
(5219229, 'amis_db/workplans'),
(5219282, 'amis_db/#sql-ib4463'),
(5219444, 'Famis_db/#sql-517c_134'),
(5219522, '!!_plan_link'),
(5219565, 'jamis_db/#sql-517c_134'),
(5219643, '!!cture_activities'),
(5219770, '!!ivities'),
(5219810, '@amis_db/#sql-517c_134'),
(5220143, 'k-)amis_db/#sql-517c_134');
DROP TABLE IF EXISTS `farmers_children`;
CREATE TABLE `farmers_children` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `farmers_children` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `field_visits`;
CREATE TABLE `field_visits` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `field_visits` (`context_offset`, `raw_text`) VALUES
(5199626, 'dipia_db/dev_nasp'),
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings');
DROP TABLE IF EXISTS `fk_parent_id`;
CREATE TABLE `fk_parent_id` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `fk_parent_id` (`context_offset`, `raw_text`) VALUES
(4948011, '{1G''~2'),
(4948067, '''Ninfimum'),
(4948084, 'supremum'),
(4948093, 'b^O6/)'),
(4948105, 'c4test_db/auth_identities_user_id_foreign'),
(4948159, 'c4test_db/auth_identitiesc4test_db/users'),
(4948203, 'lhY;4.'),
(4948215, 'c4test_db/auth_remember_tokens_user_id_foreign'),
(4948274, 'c4test_db/auth_remember_tokensc4test_db/users'),
(4948323, 'fbS81+'),
(4948335, 'c4test_db/auth_groups_users_user_id_foreign'),
(4948391, 'c4test_db/auth_groups_usersc4test_db/users'),
(4948437, 'pl]=60'),
(4948449, 'c4test_db/auth_permissions_users_user_id_foreign'),
(4948510, 'c4test_db/auth_permissions_usersc4test_db/users'),
(4948573, 'pmbx_db/bossman_ibfk_1'),
(4948608, 'pmbx_db/bossmanpmbx_db/bossman'),
(4948642, '^ZK4-'''),
(4948654, 'flarum_db/access_tokens_user_id_foreign'),
(4948705, '?flarum_db/access_tokensflarum_db/users'),
(4948748, 'TPA/("'),
(4948760, 'flarum_db/api_keys_user_id_foreign'),
(4948806, 'yflarum_db/api_keysflarum_db/users'),
(4948844, 'njU<5/'),
(4948856, 'flarum_db/discussion_user_discussion_id_foreign'),
(4948915, '@flarum_db/discussion_userflarum_db/discussions'),
(4948966, 'b^O6/)$'),
(4948977, '^flarum_db/discussion_user_user_id_foreign'),
(4949029, 'Q aflarum_db/discussion_userflarum_db/users'),
(4949076, '`\\M81+'),
(4949088, 'flarum_db/discussions_first_post_id_foreign'),
(4949144, 'flarum_db/discussionsflarum_db/posts'),
(4949184, 'a]N92,'),
(4949195, '9flarum_db/discussions_hidden_user_id_foreign'),
(4949253, 'flarum_db/discussionsflarum_db/users'),
(4949293, '_[L70*'),
(4949304, 'xflarum_db/discussions_last_post_id_foreign'),
(4949359, '[flarum_db/discussionsflarum_db/posts'),
(4949400, 'fbS>71'),
(4949412, 'flarum_db/discussions_last_posted_user_id_foreign'),
(4949473, '>flarum_db/discussionsflarum_db/users'),
(4949514, 'ZVG2+%$'),
(4949526, 'flarum_db/discussions_user_id_foreign'),
(4949575, '`flarum_db/discussionsflarum_db/users'),
(4949616, '\\XI3,&'),
(4949627, '`flarum_db/email_tokens_user_id_foreign'),
(4949678, '}flarum_db/email_tokensflarum_db/users'),
(4949720, 'fbR81+'),
(4949732, 'flarum_db/group_permission_group_id_foreign'),
(4949785, 'M&Aflarum_db/group_permissionflarum_db/groups');
DROP TABLE IF EXISTS `gov_structure`;
CREATE TABLE `gov_structure` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `gov_structure` (`context_offset`, `raw_text`) VALUES
(5204943, 'smas_db/teachers_audit_trail'),
(5205003, 'smas_db/student_assessment_files'),
(5205067, 'smas_db/assessments_answer_sheet_files'),
(5205137, 'smas_db/assessments'),
(5205188, 'smas_db/grades'),
(5205234, 'sharearound_db/rounds'),
(5205287, 'sharearound_db/round_files'),
(5205345, 'dipia_db/corporate_plan'),
(5205400, 'dipia_db/smes'),
(5205445, 'six_amis_db/dakoii_users'),
(5205501, 'six_amis_db/users'),
(5205549, 'dgrass_db/migrations'),
(5205601, 'six_amis_db/gov_structure'),
(5205658, 'six_amis_db/branches'),
(5205710, 'dipia_db/adx_province'),
(5205763, 'six_amis_db/plans_nasp'),
(5205817, 'six_amis_db/plans_corporate_plan'),
(5205881, 'six_amis_db/plans_mtdp'),
(5205935, 'six_amis_db/plans_mtdp_spa'),
(5205993, 'six_amis_db/plans_mtdp_dip'),
(5206051, 'coresta_db/dakoii_users'),
(5206222, 'coresta_db/organizations'),
(5206278, 'coresta_db/users'),
(5206326, 'coresta_db/provinces'),
(5206378, 'coresta_db/groups'),
(5206427, 'coresta_db/correspondences'),
(5206485, 'coresta_db/correspondence_refer_files'),
(5206554, 'coresta_db/correspondence_files'),
(5206617, 'coresta_db/correspondence_refer'),
(5206680, 'coresta_db/workflows'),
(5206732, 'coresta_db/workflow_history'),
(5206791, 'coresta_db/group_files'),
(5206845, 'coresta_db/correspondence_actions'),
(5206910, 'six_amis_db/sme'),
(5206957, 'six_amis_db/sme_staff'),
(5207010, 'six_amis_db/documents'),
(5207063, 'six_amis_db/folders'),
(5207114, 'six_amis_db/meetings'),
(5207166, 'dipia_db/agreements'),
(5207217, 'six_amis_db/agreements'),
(5207271, 'grass_db/adx_items'),
(5207320, 'Xgrass_db/#sql2-6290-455'),
(5207378, 'grass_db/#sql2-6290-2a7'),
(5207409, '!ation'),
(5207447, 'six_amis_db/workplan_mtdp_link'),
(5207509, 'six_amis_db/workplan_nasp_link'),
(5207571, 'six_amis_db/workplan_corporate_plan_link'),
(5208033, 'ipms_db/plans_indicators_milestones'),
(5208100, 'ipms_db/budget_book'),
(5208151, 'ipms_db/budget_codes');
DROP TABLE IF EXISTS `groupings`;
CREATE TABLE `groupings` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `groupings` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `groupingsagristats_db`;
CREATE TABLE `groupingsagristats_db` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `groupingsagristats_db` (`context_offset`, `raw_text`) VALUES
(4948011, '{1G''~2'),
(4948067, '''Ninfimum'),
(4948084, 'supremum'),
(4948093, 'b^O6/)'),
(4948105, 'c4test_db/auth_identities_user_id_foreign'),
(4948159, 'c4test_db/auth_identitiesc4test_db/users'),
(4948203, 'lhY;4.'),
(4948215, 'c4test_db/auth_remember_tokens_user_id_foreign'),
(4948274, 'c4test_db/auth_remember_tokensc4test_db/users'),
(4948323, 'fbS81+'),
(4948335, 'c4test_db/auth_groups_users_user_id_foreign'),
(4948391, 'c4test_db/auth_groups_usersc4test_db/users'),
(4948437, 'pl]=60'),
(4948449, 'c4test_db/auth_permissions_users_user_id_foreign'),
(4948510, 'c4test_db/auth_permissions_usersc4test_db/users'),
(4948573, 'pmbx_db/bossman_ibfk_1'),
(4948608, 'pmbx_db/bossmanpmbx_db/bossman'),
(4948642, '^ZK4-'''),
(4948654, 'flarum_db/access_tokens_user_id_foreign'),
(4948705, '?flarum_db/access_tokensflarum_db/users'),
(4948748, 'TPA/("'),
(4948760, 'flarum_db/api_keys_user_id_foreign'),
(4948806, 'yflarum_db/api_keysflarum_db/users'),
(4948844, 'njU<5/'),
(4948856, 'flarum_db/discussion_user_discussion_id_foreign'),
(4948915, '@flarum_db/discussion_userflarum_db/discussions'),
(4948966, 'b^O6/)$'),
(4948977, '^flarum_db/discussion_user_user_id_foreign'),
(4949029, 'Q aflarum_db/discussion_userflarum_db/users'),
(4949076, '`\\M81+'),
(4949088, 'flarum_db/discussions_first_post_id_foreign'),
(4949144, 'flarum_db/discussionsflarum_db/posts'),
(4949184, 'a]N92,'),
(4949195, '9flarum_db/discussions_hidden_user_id_foreign'),
(4949253, 'flarum_db/discussionsflarum_db/users'),
(4949293, '_[L70*'),
(4949304, 'xflarum_db/discussions_last_post_id_foreign'),
(4949359, '[flarum_db/discussionsflarum_db/posts'),
(4949400, 'fbS>71'),
(4949412, 'flarum_db/discussions_last_posted_user_id_foreign'),
(4949473, '>flarum_db/discussionsflarum_db/users'),
(4949514, 'ZVG2+%$'),
(4949526, 'flarum_db/discussions_user_id_foreign'),
(4949575, '`flarum_db/discussionsflarum_db/users'),
(4949616, '\\XI3,&'),
(4949627, '`flarum_db/email_tokens_user_id_foreign'),
(4949678, '}flarum_db/email_tokensflarum_db/users'),
(4949720, 'fbR81+'),
(4949732, 'flarum_db/group_permission_group_id_foreign'),
(4949785, 'M&Aflarum_db/group_permissionflarum_db/groups');
DROP TABLE IF EXISTS `inputs`;
CREATE TABLE `inputs` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `inputs` (`context_offset`, `raw_text`) VALUES
(5199626, 'dipia_db/dev_nasp'),
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings');
DROP TABLE IF EXISTS `livestock_farm_blocks`;
CREATE TABLE `livestock_farm_blocks` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `livestock_farm_blocks` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `livestock_farm_data`;
CREATE TABLE `livestock_farm_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `livestock_farm_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `livestock_production_data`;
CREATE TABLE `livestock_production_data` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `livestock_production_data` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `permissions_items`;
CREATE TABLE `permissions_items` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `permissions_items` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `permissions_sets`;
CREATE TABLE `permissions_sets` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `permissions_sets` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `permissions_user_districts`;
CREATE TABLE `permissions_user_districts` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `permissions_user_districts` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `permissions_user_districts_`;
CREATE TABLE `permissions_user_districts_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `permissions_user_districts_` (`context_offset`, `raw_text`) VALUES
(5767165, '>#"HNK'),
(5767269, 'infimum'),
(5767284, 'supremum'),
(5767309, 'sendme_db/adx_country'),
(5767346, 'sendme_db/adx_district'),
(5767384, 'sendme_db/adx_llg'),
(5767417, 'sendme_db/adx_province'),
(5767455, 'sendme_db/adx_ward'),
(5767489, 'sendme_db/dakoii_org'),
(5767525, 'sendme_db/dakoii_users'),
(5767563, 'sendme_db/selection'),
(5767598, 'sendme_db/settings'),
(5767632, 'sendme_db/users'),
(5767663, 'sendme_db/post_files'),
(5767699, 'sendme_db/post_texts'),
(5767735, 'sendme_db/post_comments'),
(5767774, 'pdr360_db/employees'),
(5767809, 'promis_db/projects'),
(5767842, '6pdr360_db/feedback_data"'),
(5767881, '9pdr360_db/skills_comp_data'),
(5767924, 'part3_db/adx_country'),
(5767960, 'part3_db/adx_district'),
(5767997, 'part3_db/adx_llg'),
(5768029, 'part3_db/adx_province'),
(5768066, 'part3_db/adx_ward'),
(5768099, 'part3_db/dakoii_org'),
(5768134, 'part3_db/dakoii_users'),
(5768171, 'part3_db/events'),
(5768202, 'part3_db/groups'),
(5768233, 'part3_db/plans'),
(5768263, 'part3_db/plans_kpi'),
(5768297, 'part3_db/plans_kra"'),
(5768331, 'part3_db/plan_deliverables'),
(5768373, 'part3_db/plan_indicators('),
(5768413, 'part3_db/plan_indicators_targets&'),
(5768461, 'part3_db/plan_key_deliverables'),
(5768507, 'part3_db/plan_objectives'),
(5768547, 'part3_db/plan_projects*'),
(5768585, 'part3_db/plan_projects_allocations'),
(5768635, 'part3_db/plan_strategies'),
(5768675, 'part3_db/positions'),
(5768709, 'part3_db/selection'),
(5768743, 'part3_db/settings'),
(5768776, 'part3_db/users'),
(5768806, 'part3_db/bossman'),
(5768838, 'part_db/adx_country'),
(5768872, '"part_db/adx_district'),
(5768908, '%part_db/adx_llg'),
(5768939, '(part_db/adx_province'),
(5768975, '+part_db/adx_ward');
DROP TABLE IF EXISTS `selection`;
CREATE TABLE `selection` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `selection` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `settings` (`context_offset`, `raw_text`) VALUES
(5193829, 'infimum'),
(5193844, 'supremum'),
(5193881, 'dipia_db/adx_district'),
(5193934, 'dipia_db/adx_llg'),
(5193982, 'dipia_db/selection'),
(5194032, 'dipia_db/settings'),
(5194081, 'dipia_db/adx_country'),
(5194133, 'selsys_db/dakoii_users'),
(5194187, 'dipia_db/dakoii_users'),
(5194240, 'dipia_db/stakeholders'),
(5194293, 'dipia_db/level_groups'),
(5194346, 'dipia_db/dev_dip'),
(5194394, 'dipia_db/dev_specific_areas'),
(5194453, 'dipia_db/dev_investments'),
(5194509, 'dipia_db/dev_strategies'),
(5194564, 'dipia_db/dev_indicators'),
(5194619, 'dipia_db/dev_kra'),
(5194667, 'dipia_db/sme_staff'),
(5194717, 'dipia_db/dev_spa'),
(5194765, 'dipia_db/dev_mtdp'),
(5194814, 'dipia_db/adx_village'),
(5194866, 'dipia_db/wk_workplan'),
(5194918, 'dipia_db/act_documents'),
(5194972, 'dipia_db/act_infra_details'),
(5195030, 'dipia_db/act_meeting'),
(5195082, 'dipia_db/act_meeting_details'),
(5195142, 'dipia_db/act_input_details'),
(5195200, 'dipia_db/act_assign_users'),
(5195257, 'dipia_db/act_proposals'),
(5195311, 'dipia_db/act_training_details'),
(5195372, 'dipia_db/org_branches'),
(5195425, 'dipia_db/adx_ward'),
(5195474, 'dipia_db/act_agreements'),
(5195529, 'selmasta_db/dakoii_org'),
(5195583, 'selmasta_db/dakoii_users'),
(5195639, 'selmasta_db/selection'),
(5195692, 'selmasta_db/users'),
(5195741, 'selsys_db/users'),
(5195788, 'selmasta_db/positions_groups'),
(5195848, 'selmasta_db/positions'),
(5195901, 'payrollwan_db/adx_country'),
(5195958, 'payrollwan_db/adx_district'),
(5196016, 'payrollwan_db/adx_llg'),
(5196069, 'payrollwan_db/adx_province'),
(5196127, 'payrollwan_db/adx_ward'),
(5196181, 'payrollwan_db/dakoii_org'),
(5196237, 'payrollwan_db/dakoii_users'),
(5196295, 'payrollwan_db/earnings_deductions'),
(5196360, 'payrollwan_db/employees'),
(5196415, 'payrollwan_db/groups');
DROP TABLE IF EXISTS `trainings`;
CREATE TABLE `trainings` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `trainings` (`context_offset`, `raw_text`) VALUES
(5199626, 'dipia_db/dev_nasp'),
(5199675, 'dipia_db/remindertable'),
(5199759, '!_data'),
(5199797, 'eccq_db/adx_country'),
(5199848, 'eccq_db/adx_district'),
(5199900, 'eccq_db/adx_llg'),
(5199947, 'eccq_db/adx_province'),
(5199999, 'eccq_db/adx_ward'),
(5200047, 'eccq_db/dakoii_org'),
(5200097, 'eccq_db/dakoii_users'),
(5200149, 'eccq_db/selection'),
(5200198, 'eccq_db/settings'),
(5200246, 'eccq_db/users'),
(5200291, 'eccq_db/query_persons'),
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings');
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `users` (`context_offset`, `raw_text`) VALUES
(1491265, 'theme_mods_charity-zone'),
(1491496, 'wptest_db'),
(1491506, 'wp_posts'),
(1491515, 'PRIMARY'),
(1491523, 'n_diff_pfx01'),
(1491754, 'n_leaf_pages'),
(1492156, 'post_author'),
(1492405, 'n_diff_pfx02'),
(1493082, 'post_name'),
(1493986, 'post_parent'),
(1494912, 'type_status_date'),
(1495453, 'n_diff_pfx03'),
(1495720, 'n_diff_pfx04'),
(1497377, 'position'),
(1497459, 'id_photo'),
(1497675, 'status'),
(1497755, 'status_at'),
(1497838, 'status_by'),
(1497915, 'status_remarks'),
(1498003, 'created_by'),
(1498087, 'updated_by'),
(1498171, 'created_at'),
(1498255, 'updated_at'),
(1498339, 'is_admin'),
(1498421, 'is_supervisor'),
(1501200, 'agristats_db'),
(1501213, 'adx_livestock('),
(1818928, 'publish'),
(1818965, '$a7cfe3d4-e8f9-4adc-af6d-fbad158629f8'),
(1819014, 'customize_changeset'),
(1819450, '.\\agristats_db\\#sql-fe98_2f4.ibd'),
(1819704, '#mysql50##sql2-fe98-562'),
(1823886, 'adx_ward'),
(2507029, 'pamis_db/workplan_activities'),
(2507079, 'amis_db/workplan_activities'),
(2507155, 'amis_db/#sql2-517c-134'),
(2507261, '!.\\amis_db\\workplan_activities.ibd'),
(2507503, 'amis_db'),
(2507511, '#mysql50##sql-517c_134'),
(2507584, 'workplan_training_activities'),
(2507806, 'ders_db'),
(2507814, 'applicant_files'),
(2507978, 'idx_applicant_id'),
(2508214, 'idx_created_by'),
(2508442, 'idx_updated_by'),
(2508845, 'adx_llg'),
(2510035, 'org_id'),
(2510175, 'password'),
(2511746, 'pcollx_db/geo_provinces'),
(2512223, 'pending');
DROP TABLE IF EXISTS `users8_6fc`;
CREATE TABLE `users8_6fc` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `users8_6fc` (`context_offset`, `raw_text`) VALUES
(5781424, 'promis_two_db/organization_images'),
(5781473, 'promis_two_db/users'),
(5781508, 'promis_two_db/projects$'),
(5781546, 'promis_two_db/project_phases('),
(5781590, 'promis_two_db/project_milestones)'),
(5781638, 'promis_two_db/project_contractors&'),
(5781687, 'promis_two_db/project_officers*'),
(5781733, 'promis_two_db/project_budget_items&'),
(5781783, 'promis_two_db/project_expenses&'),
(5781829, 'promis_two_db/project_outcomes.'),
(5781875, 'promis_two_db/project_issues_addressed'''),
(5781929, 'promis_two_db/project_documents#'),
(5781976, 'promis_two_db/project_risks'),
(5782019, 'promis_two_db/migrations'),
(5782059, 'promis_two_db/audit_logs/'),
(5782099, 'promis_two_db/project_impact_indicators'''),
(5782154, 'ders_db/appx_application_rating!'),
(5782201, 'promis_two_db/contractors*'),
(5782242, 'promis_two_db/contractor_documents+'),
(5782292, 'promis_two_db/contractor_compliance,'),
(5782343, 'promis_two_db/contractor_assessments,'),
(5782395, 'promis_two_db/contractor_evaluations-'),
(5782447, 'promis_two_db/project_milestone_files$'),
(5782500, 'promis_two_db/project_events)'),
(5782543, 'promis_two_db/project_event_files'),
(5782592, '&ders_db/exercises'),
(5782625, ')ders_db/dakoii_org'),
(5782659, ',amis_db/#sql-ib4435'),
(5782694, ',amis_db/agreements'),
(5782728, '-amis_db/branches'),
(5782760, '.amis_db/commodities'),
(5782795, '/amis_db/commodity_prices$'),
(5782835, '0amis_db/commodity_production'),
(5782879, '-amis_db/#sql-ib4436'),
(5782914, '1amis_db/#sql-ib4440'),
(5783653, 'infimum'),
(5783668, 'supremum'),
(5783692, '6amis_db/#sql-ib4445s'),
(5783729, 'amis_db/foldersts'),
(5783761, '3amis_db/folders'),
(5783793, 'amis_db/meetingscture'),
(5783829, '5amis_db/meetings'),
(5783861, '6amis_db/migrations'),
(5783895, '7amis_db/org_settings'),
(5783932, 'amis_db/plans_mtdporate_plan'),
(5783975, '9amis_db/plans_mtdp'),
(5784009, ':amis_db/plans_mtdp_dip%'),
(5784047, ';amis_db/plans_mtdp_indicators'),
(5784093, 'amis_db/plans_mtdp_kraestments'),
(5784138, '=amis_db/plans_mtdp_kra');
DROP TABLE IF EXISTS `workplan_infrastructure_activities`;
CREATE TABLE `workplan_infrastructure_activities` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `workplan_infrastructure_activities` (`context_offset`, `raw_text`) VALUES
(5200344, 'eccq_db/query_files'),
(5200395, 'eccq_db/queries'),
(5200442, 'eccq_db/query_followups'),
(5200497, 'eccq_db/query_followup_files'),
(5200886, 'dipia_db/wk_workplan_details'),
(5200946, 'dipia_db/migrations'),
(5200997, 'dipia_db/act_infrastructure'),
(5201056, 'dipia_db/act_inputs'),
(5201230, 'dipia_db/permissions_items'),
(5201288, 'dipia_db/permissions_sets'),
(5201345, 'govps_db/adx_country'),
(5201397, 'govps_db/adx_district'),
(5201450, 'govps_db/adx_education'),
(5201504, 'govps_db/adx_llg'),
(5201552, 'govps_db/adx_province'),
(5201605, 'govps_db/adx_ward'),
(5201654, 'govps_db/dakoii_org'),
(5201705, 'govps_db/dakoii_users'),
(5201758, 'govps_db/selection'),
(5201808, 'govps_db/settings'),
(5201857, 'govps_db/workplan'),
(5201906, 'govps_db/groupings'),
(5201956, 'govps_db/users'),
(5202002, 'govps_db/positions'),
(5202052, 'govps_db/payslips'),
(5202101, 'govps_db/letters'),
(5202149, 'govps_db/migrations'),
(5202200, 'govps_db/employees'),
(5202250, 'grass_db/adx_district'),
(5202303, 'grass_db/adx_education'),
(5202357, 'grass_db/adx_llg'),
(5202405, 'grass_db/adx_ward'),
(5202454, 'grass_db/dakoii_org'),
(5202505, 'grass_db/dakoii_users'),
(5202558, 'grass_db/selection'),
(5202605, 'C1Bgrass_db/settings'),
(5202657, 'grass_db/positions'),
(5202707, 'grass_db/exercises'),
(5202757, 'dipia_db/users'),
(5202803, 'grass_db/positions_groups'),
(5202860, 'grass_db/applicants'),
(5202909, '$ggrass_db/#sql2-6290-21d'),
(5202974, 'grass_db/#sql2-6290-233'),
(5203034, 'grass_db/#sql2-6290-273'),
(5203090, 'ipms_db/dakoii_users'),
(5203142, 'ipms_db/dakoii_org'),
(5203192, 'ipms_db/org_users'),
(5203241, 'ipms_db/work_years'),
(5203291, 'ipms_db/plans'),
(5203403, 'ipms_db/plans_programs_kras');
DROP TABLE IF EXISTS `workplan_infrastructure_activities_`;
CREATE TABLE `workplan_infrastructure_activities_` ( `id` BIGINT AUTO_INCREMENT PRIMARY KEY, `context_offset` BIGINT, `raw_text` LONGTEXT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO `workplan_infrastructure_activities_` (`context_offset`, `raw_text`) VALUES
(5770866, 'payrollwan_db/adx_district'),
(5770908, 'payrollwan_db/adx_llg"'),
(5770945, 'payrollwan_db/adx_province'),
(5770987, 'payrollwan_db/adx_ward'),
(5771025, 'payrollwan_db/dakoii_org"'),
(5771065, 'payrollwan_db/dakoii_users)'),
(5771107, 'payrollwan_db/earnings_deductions'),
(5771156, 'payrollwan_db/employees'),
(5771195, 'payrollwan_db/groups'),
(5771231, 'payrollwan_db/payrolls'),
(5771268, '"payrollwan_db/payslips'),
(5771306, '%payrollwan_db/pay_period'),
(5771346, '(payrollwan_db/positions&'),
(5771385, '+payrollwan_db/positions_groups'),
(5771431, '.payrollwan_db/selection'),
(5771470, '1payrollwan_db/settings'),
(5771508, '4payrollwan_db/users'),
(5771543, '<selmasta_db/applicants'),
(5771581, 'Ahrwan_db/employee_files'),
(5771620, 'Bhrwan_db/groups'),
(5771651, 'Chrwan_db/payrolls'),
(5771684, 'Dhrwan_db/payslips'),
(5771717, 'Ehrwan_db/pay_period'),
(5771752, 'Fhrwan_db/positions!'),
(5771786, 'Ghrwan_db/positions_groups$'),
(5771827, 'Hhrwan_db/earnings_deductions'),
(5771871, 'Ihrwan_db/employees'),
(5771905, 'Khrwan_db/emp_leave'),
(5771939, 'Lhrwan_db/migrations'),
(5771974, 'Mhrwan_db/employment_logs-'),
(5772014, 'Ndipia_db/stakeholders_reports_details,'),
(5772067, 'Odipia_db/stakeholders_reports_header'),
(5773003, 'dipia_db/commodity_boards'),
(5773044, 'dipia_db/crops$'),
(5773074, 'dipia_db/production_data_sme1'),
(5773118, 'dipia_db/production_data_commodity_boards*'),
(5773175, 'dipia_db/production_data_provinces'),
(5773225, 'dipia_db/act_training'),
(5773472, 'dipia_db/dev_nasp'),
(5773505, 'dipia_db/remindertable,'),
(5773594, '$eccq_db/adx_country'),
(5773629, '*eccq_db/adx_district'),
(5773665, '6eccq_db/adx_llg'),
(5773696, '<eccq_db/adx_province'),
(5773732, '?eccq_db/adx_ward'),
(5773764, 'Keccq_db/dakoii_org'),
(5773798, 'Neccq_db/dakoii_users'),
(5773835, 'eccq_db/selection'),
(5773868, 'eccq_db/settings'),
(5773900, 'eccq_db/users');