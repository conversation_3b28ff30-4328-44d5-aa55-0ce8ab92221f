2025-08-09  9:28:35 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 14792
2025-08-09  9:28:35 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09  9:28:35 0 [Note] InnoDB: Uses event mutexes
2025-08-09  9:28:35 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-08-09  9:28:35 0 [Note] InnoDB: Number of pools: 1
2025-08-09  9:28:35 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09  9:28:35 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09  9:28:35 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09  9:28:35 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=300288
2025-08-09  9:28:35 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09  9:28:35 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09  9:28:35 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09  9:28:35 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09  9:28:35 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09  9:28:35 0 [Note] InnoDB: 10.4.32 started; log sequence number 300297; transaction id 170
2025-08-09  9:28:35 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-08-09  9:28:35 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09  9:28:35 0 [Note] Server socket created on IP: '::'.
2025-08-09 09:28:35 0x1f9c  InnoDB: Assertion failure in file D:\winx64-packages\build\src\storage\innobase\os\os0file.cc line 6132
InnoDB: Failing assertion: slot
InnoDB: We intentionally generate a memory trap.
InnoDB: Submit a detailed bug report to https://jira.mariadb.org/
InnoDB: If you get repeated assertion failures or crashes, even
InnoDB: immediately after the mysqld startup, there may be
InnoDB: corruption in the InnoDB tablespace. Please refer to
InnoDB: https://mariadb.com/kb/en/library/innodb-recovery-modes/
InnoDB: about forcing recovery.
2025-08-09  9:28:35 0 [Note] InnoDB: Buffer pool(s) load completed at 250809  9:28:35
250809  9:28:35 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=6
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
2025-08-09  9:28:35 0 [Note] Reading of all Master_info entries succeeded
2025-08-09  9:28:35 0 [Note] Added new Master_info '' to hash table
2025-08-09  9:28:35 0 [Note] c:\xampp\mysql\bin\mysqld.exe: ready for connections.
Version: '10.4.32-MariaDB'  socket: ''  port: 3306  mariadb.org binary distribution
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-08-09  9:28:47 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 14116
2025-08-09  9:28:47 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09  9:28:47 0 [Note] InnoDB: Uses event mutexes
2025-08-09  9:28:47 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-08-09  9:28:47 0 [Note] InnoDB: Number of pools: 1
2025-08-09  9:28:47 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09  9:28:47 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09  9:28:47 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09  9:28:47 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=300306
2025-08-09  9:28:47 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09  9:28:47 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09  9:28:47 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09  9:28:47 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09  9:28:47 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09  9:28:47 0 [Note] InnoDB: 10.4.32 started; log sequence number 300315; transaction id 170
2025-08-09  9:28:47 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-08-09  9:28:47 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09  9:28:47 0 [Note] Server socket created on IP: '::'.
2025-08-09  9:32:17 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 5124
2025-08-09  9:32:17 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09  9:32:17 0 [Note] InnoDB: Uses event mutexes
2025-08-09  9:32:17 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-08-09  9:32:17 0 [Note] InnoDB: Number of pools: 1
2025-08-09  9:32:17 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09  9:32:17 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09  9:32:17 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09  9:32:17 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=303792
2025-08-09  9:32:17 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09  9:32:17 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09  9:32:17 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09  9:32:17 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09  9:32:17 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09  9:32:17 0 [Note] InnoDB: 10.4.32 started; log sequence number 303801; transaction id 193
2025-08-09  9:32:17 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-08-09  9:32:17 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09  9:32:17 0 [Note] Server socket created on IP: '::'.
2025-08-09  9:43:49 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22604
2025-08-09  9:43:49 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09  9:43:49 0 [Note] InnoDB: Uses event mutexes
2025-08-09  9:43:49 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-08-09  9:43:49 0 [Note] InnoDB: Number of pools: 1
2025-08-09  9:43:49 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09  9:43:49 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09  9:43:49 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09  9:43:49 0 [Note] InnoDB: Setting log file C:\xampp\mysql\data\ib_logfile101 size to 5242880 bytes
2025-08-09  9:43:49 0 [Note] InnoDB: Setting log file C:\xampp\mysql\data\ib_logfile1 size to 5242880 bytes
2025-08-09  9:43:49 0 [Note] InnoDB: Renaming log file C:\xampp\mysql\data\ib_logfile101 to C:\xampp\mysql\data\ib_logfile0
2025-08-09  9:43:49 0 [Note] InnoDB: New log files created, LSN=388249
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=7] log sequence number 2786881 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=3] log sequence number 35383102 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=4] log sequence number 6007312 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=11] log sequence number 980613 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=375] log sequence number 1065026 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=1] log sequence number 6665831 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=6] log sequence number 3065097 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=639] log sequence number 3065097 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=45] log sequence number 3065136 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=503] log sequence number 3065136 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=46] log sequence number 3065175 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=414] log sequence number 3065175 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=49] log sequence number 3065292 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=505] log sequence number 3065292 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=52] log sequence number 3065549 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=55] log sequence number 3065666 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=465] log sequence number 3065666 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=60] log sequence number 3066217 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=61] log sequence number 3066318 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=62] log sequence number 3066357 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=493] log sequence number 3066357 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=63] log sequence number 3066412 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=474] log sequence number 3066412 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=192] log sequence number 3066456 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=478] log sequence number 3066456 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=193] log sequence number 3066500 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=320] log sequence number 3066500 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=197] log sequence number 3066676 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=323] log sequence number 3066676 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=205] log sequence number 3145182 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=329] log sequence number 3145182 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=208] log sequence number 3145330 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=494] log sequence number 3145330 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=211] log sequence number 3145462 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=473] log sequence number 4066845 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=214] log sequence number 2584495 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=449] log sequence number 2584495 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=217] log sequence number 3145780 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=450] log sequence number 3145780 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=220] log sequence number 3145912 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=315] log sequence number 3145912 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=223] log sequence number 3146107 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=226] log sequence number 4009005 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=316] log sequence number 4009005 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=229] log sequence number 4009137 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=361] log sequence number 4009137 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=232] log sequence number 4009269 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=585] log sequence number 4009269 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=235] log sequence number 4009401 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=481] log sequence number 4009401 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=238] log sequence number 4009549 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=457] log sequence number 4009549 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=241] log sequence number 4047729 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=346] log sequence number 4047729 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=245] log sequence number 4047861 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=607] log sequence number 4047861 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=248] log sequence number 4048072 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=251] log sequence number 4048204 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=347] log sequence number 4048204 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=254] log sequence number 4048336 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=598] log sequence number 4048336 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=257] log sequence number 4048484 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=448] log sequence number 4048484 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=258] log sequence number 4048591 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=259] log sequence number 4048635 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=349] log sequence number 4048635 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=260] log sequence number 4048679 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=350] log sequence number 4048679 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=261] log sequence number 4048723 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=369] log sequence number 4048723 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=262] log sequence number 4047597 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=470] log sequence number 4047597 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=263] log sequence number 4053042 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=586] log sequence number 4053042 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=264] log sequence number 4053329 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=601] log sequence number 4053329 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=265] log sequence number 4054153 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=334] log sequence number 4054153 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=266] log sequence number 4054659 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=435] log sequence number 4054659 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=267] log sequence number 4054971 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=388] log sequence number 4054971 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=268] log sequence number 4056527 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=370] log sequence number 4056527 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=269] log sequence number 4056855 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=439] log sequence number 4056855 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=270] log sequence number 4058276 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=452] log sequence number 4058276 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=271] log sequence number 4058606 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=472] log sequence number 4058606 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=272] log sequence number 4058989 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=469] log sequence number 4058989 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=273] log sequence number 4062917 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=336] log sequence number 4062917 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=274] log sequence number 4066845 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=275] log sequence number 4067248 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=495] log sequence number 4067248 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=276] log sequence number 4072226 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=351] log sequence number 4072226 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=277] log sequence number 4072529 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=603] log sequence number 4072529 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=278] log sequence number 4074686 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=371] log sequence number 4074686 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=279] log sequence number 4076593 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=509] log sequence number 4076593 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=280] log sequence number 4080511 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=337] log sequence number 4080511 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=281] log sequence number 2996269 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=463] log sequence number 2996269 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=282] log sequence number 2996313 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=338] log sequence number 2996313 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=283] log sequence number 2996357 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=475] log sequence number 2996357 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=284] log sequence number 2996401 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=445] log sequence number 2996401 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=285] log sequence number 2996445 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=453] log sequence number 2996445 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=286] log sequence number 2996489 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=604] log sequence number 2996489 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=287] log sequence number 2996533 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=339] log sequence number 2996533 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=288] log sequence number 2996577 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=340] log sequence number 2996577 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=289] log sequence number 2996621 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=502] log sequence number 2996621 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=290] log sequence number 2996665 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=341] log sequence number 2996665 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=291] log sequence number 2996709 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=476] log sequence number 2996709 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=292] log sequence number 3002511 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=464] log sequence number 3002511 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=293] log sequence number 3002555 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=490] log sequence number 3002555 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=294] log sequence number 3002599 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=491] log sequence number 3002599 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=295] log sequence number 3002643 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=595] log sequence number 3002643 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=296] log sequence number 3002687 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=355] log sequence number 3002687 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=297] log sequence number 3002731 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=638] log sequence number 3002731 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=298] log sequence number 3002775 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=588] log sequence number 3002775 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=299] log sequence number 3002819 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=436] log sequence number 3002819 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=300] log sequence number 3066764 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=596] log sequence number 3066764 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=8] log sequence number 2758856 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=497] log sequence number 2758856 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=10] log sequence number 2765987 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=379] log sequence number 1065044 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=12] log sequence number 2764845 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=485] log sequence number 1065020 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=307] log sequence number 2677521 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=302] log sequence number 514660 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=303] log sequence number 508091 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=304] log sequence number 508175 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=308] log sequence number 2618382 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=462] log sequence number 2758838 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=486] log sequence number 2793078 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=345] log sequence number 7135669 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\adx_country.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`adx_country` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=499] log sequence number 2758856 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\adx_district.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`adx_district` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\adx_llg.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`adx_llg` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\adx_province.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`adx_province` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\adx_ward.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`adx_ward` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\appslink.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`appslink``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`appslink` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\selection.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`selection` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\settings.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`settings` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\apps_link\users.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``apps_link`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `apps_link`.`users` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Page [page id: space=0, page number=500] log sequence number 6311485 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\activities.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`activities``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`activities` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\areas_assess.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`areas_assess``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`areas_assess` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\assess_comp.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`assess_comp``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`assess_comp` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\assess_inactivity.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`assess_inactivity``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`assess_inactivity` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\emp_feedbacks.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`emp_feedbacks``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`emp_feedbacks` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\emp_ingroups.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`emp_ingroups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`emp_ingroups` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\empgroups.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`empgroups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`empgroups` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\employees.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`employees``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`employees` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\feedback_data.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`feedback_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`feedback_data` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\feedbacks_ingroups.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`feedbacks_ingroups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`feedbacks_ingroups` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\model_components.ibd' OS error: 203
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:49 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:49 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`model_components``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:49 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`model_components` because it could not be opened.
2025-08-09  9:43:49 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:49 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:49 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\qgroup_comp.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`qgroup_comp``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`qgroup_comp` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\qgroups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`qgroups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`qgroups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\questions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`questions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`questions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\questions_inactivity.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`questions_inactivity``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`questions_inactivity` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\questions_ingroups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`questions_ingroups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`questions_ingroups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\rates_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`rates_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`rates_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\solutions_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`solutions_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`solutions_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\ocna_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``ocna_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `ocna_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\bossman.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`bossman``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`bossman` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\events.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`events``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`events` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_deliverables.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_deliverables``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_deliverables` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_indicators.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_indicators``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_indicators` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_indicators_targets.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_indicators_targets``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_indicators_targets` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_key_deliverables.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_key_deliverables``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_key_deliverables` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_objectives.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_objectives``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_objectives` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_projects.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_projects``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_projects` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_projects_allocations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_projects_allocations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_projects_allocations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plan_strategies.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plan_strategies``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plan_strategies` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plans.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plans``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plans` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plans_kpi.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plans_kpi``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plans_kpi` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\plans_kra.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`plans_kra``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`plans_kra` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\positions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`positions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`positions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part3_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part3_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part3_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Page [page id: space=0, page number=454] log sequence number 2792973 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_exercise.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_exercise``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_exercise` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_items_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_items_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_items_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_plans.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_plans``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_plans` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_report_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_report_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_report_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\assess_report_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`assess_report_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`assess_report_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\part_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``part_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `part_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\earnings_deductions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`earnings_deductions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`earnings_deductions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\employees.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`employees``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`employees` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\pay_period.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`pay_period``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`pay_period` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\payrolls.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`payrolls``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`payrolls` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\payslips.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`payslips``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`payslips` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\positions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`positions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`positions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\positions_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`positions_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`positions_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\payrollwan_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``payrollwan_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `payrollwan_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\activities.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`activities``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`activities` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\activity_business_locations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`activity_business_locations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`activity_business_locations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\activity_price_collection_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`activity_price_collection_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`activity_price_collection_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\activity_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`activity_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`activity_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\business_entities.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`business_entities``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`business_entities` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\business_locations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`business_locations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`business_locations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\geo_countries.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`geo_countries``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`geo_countries` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\geo_districts.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`geo_districts``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`geo_districts` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\geo_provinces.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`geo_provinces``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`geo_provinces` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\goods_brands.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`goods_brands``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`goods_brands` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\goods_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`goods_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`goods_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\goods_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`goods_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`goods_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\price_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`price_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`price_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pcollx_db\workplans.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pcollx_db`.`workplans``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pcollx_db`.`workplans` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\activities.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`activities``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`activities` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\appraisers_inactivity.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`appraisers_inactivity``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`appraisers_inactivity` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\emp_appraisers.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`emp_appraisers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`emp_appraisers` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\employees.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`employees``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`employees` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\feedback_categories.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`feedback_categories``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`feedback_categories` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\feedback_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`feedback_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`feedback_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\feedback_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`feedback_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`feedback_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\feedback_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`feedback_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`feedback_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\skills_comp_categories.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`skills_comp_categories``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`skills_comp_categories` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\skills_comp_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`skills_comp_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`skills_comp_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\skills_comp_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`skills_comp_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`skills_comp_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\skills_comp_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`skills_comp_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`skills_comp_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pdr360_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pdr360_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pdr360_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\bossman.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`bossman``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`bossman` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\events.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`events``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`events` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_deliverables.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_deliverables``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_deliverables` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_indicators.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_indicators``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_indicators` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_indicators_targets.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_indicators_targets``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_indicators_targets` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_key_deliverables.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_key_deliverables``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_key_deliverables` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_objectives.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_objectives``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_objectives` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_projects.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_projects``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_projects` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_projects_allocations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_projects_allocations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_projects_allocations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plan_strategies.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plan_strategies``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plan_strategies` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plans.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plans``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plans` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plans_kpi.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plans_kpi``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plans_kpi` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\plans_kra.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`plans_kra``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`plans_kra` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\positions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`positions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`positions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\pmbx_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``pmbx_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `pmbx_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\contractor_details.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`contractor_details``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`contractor_details` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\contractor_files.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`contractor_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`contractor_files` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\contractor_notices.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`contractor_notices``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`contractor_notices` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\kmlfiles.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`kmlfiles``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`kmlfiles` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\profund.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`profund``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`profund` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_eventfiles.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_eventfiles``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_eventfiles` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_events.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_events``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_events` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_files.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_files` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_milefiles.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_milefiles``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_milefiles` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_milestones.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_milestones``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_milestones` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_officers.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_officers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_officers` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_phases.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_phases``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_phases` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\project_tasks.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`project_tasks``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`project_tasks` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\projects.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`projects``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`projects` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\audit_logs.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`audit_logs``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`audit_logs` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\contractor_assessments.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`contractor_assessments``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`contractor_assessments` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\contractor_compliance.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`contractor_compliance``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`contractor_compliance` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\contractor_documents.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`contractor_documents``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`contractor_documents` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\contractor_evaluations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`contractor_evaluations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`contractor_evaluations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\contractors.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`contractors``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`contractors` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\countries.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`countries``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`countries` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\districts.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`districts``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`districts` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\llgs.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`llgs``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`llgs` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\migrations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`migrations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`migrations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\organization_images.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`organization_images``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`organization_images` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Page [page id: space=0, page number=511] log sequence number 5112150 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\organizations.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`organizations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`organizations` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_budget_items.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_budget_items``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_budget_items` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_contractors.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_contractors``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_contractors` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_documents.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_documents``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_documents` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_event_files.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_event_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_event_files` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_events.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_events``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_events` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_expenses.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_expenses``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_expenses` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_impact_indicators.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_impact_indicators``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_impact_indicators` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_issues_addressed.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_issues_addressed``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_issues_addressed` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_milestone_files.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_milestone_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_milestone_files` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_milestones.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_milestones``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_milestones` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_officers.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_officers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_officers` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_outcomes.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_outcomes``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_outcomes` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_phases.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_phases``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_phases` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\project_risks.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`project_risks``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`project_risks` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\projects.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`projects``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`projects` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\provinces.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`provinces``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`provinces` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\promis_two_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``promis_two_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `promis_two_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\competitions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`competitions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`competitions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\countries.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`countries``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`countries` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\events.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`events``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`events` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\teams.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`teams``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`teams` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\rc4_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``rc4_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `rc4_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\applicants.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`applicants``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`applicants` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\interview_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`interview_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`interview_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\interview_questions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`interview_questions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`interview_questions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\interviewers.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`interviewers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`interviewers` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\positions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`positions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`positions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\positions_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`positions_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`positions_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selmasta_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selmasta_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selmasta_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\activities.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`activities``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`activities` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\applicants_info.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`applicants_info``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`applicants_info` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\int_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`int_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`int_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\interviewees.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`interviewees``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`interviewees` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\interviewers.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`interviewers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`interviewers` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\positions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`positions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`positions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\positions_groups.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`positions_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`positions_groups` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\profile_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`profile_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`profile_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\questions.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`questions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`questions` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\rep_settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`rep_settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`rep_settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\req_data.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`req_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`req_data` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\selection.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`selection` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\settings.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`settings` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\selsys_db\users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``selsys_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `selsys_db`.`users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\post_comments.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:50 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`post_comments``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:50 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`post_comments` because it could not be opened.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:50 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:50 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:50 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\post_files.ibd' OS error: 203
2025-08-09  9:43:50 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:50 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`post_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`post_files` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\post_texts.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`post_texts``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`post_texts` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\selection.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`selection` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\settings.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`settings` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sendme_db\users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sendme_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sendme_db`.`users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sharearound_db\round_files.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sharearound_db`.`round_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sharearound_db`.`round_files` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\sharearound_db\rounds.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``sharearound_db`.`rounds``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `sharearound_db`.`rounds` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\agreements.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`agreements``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`agreements` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\branches.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`branches``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`branches` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\documents.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`documents``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`documents` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\folders.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`folders``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`folders` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\gov_structure.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`gov_structure``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`gov_structure` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\meetings.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`meetings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`meetings` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_corporate_plan.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_corporate_plan``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_corporate_plan` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_dip.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_dip``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_dip` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_indicators.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_indicators``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_indicators` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_investments.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_investments``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_investments` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_kra.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_kra``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_kra` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_spa.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_spa``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_spa` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_specific_area.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_specific_area``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_specific_area` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_mtdp_strategies.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_mtdp_strategies``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_mtdp_strategies` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\plans_nasp.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`plans_nasp``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`plans_nasp` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\proposal.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`proposal``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`proposal` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\sme.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`sme``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`sme` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\sme_staff.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`sme_staff``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`sme_staff` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\training_details.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`training_details``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`training_details` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\training_header.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`training_header``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`training_header` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\workplan_corporate_plan_link.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`workplan_corporate_plan_link``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`workplan_corporate_plan_link` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\workplan_details.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`workplan_details``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`workplan_details` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\workplan_header.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`workplan_header``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`workplan_header` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\workplan_mtdp_link.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`workplan_mtdp_link``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`workplan_mtdp_link` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\six_amis_db\workplan_nasp_link.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``six_amis_db`.`workplan_nasp_link``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `six_amis_db`.`workplan_nasp_link` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_country.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_country``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_country` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_district.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_district``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_district` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_education.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_education``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_education` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_llg.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_llg``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_llg` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_province.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_province``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_province` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\adx_ward.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`adx_ward``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`adx_ward` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\assessments.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`assessments``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`assessments` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\assessments_answer_sheet_files.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`assessments_answer_sheet_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`assessments_answer_sheet_files` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\class.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`class``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`class` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\class_student_join.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`class_student_join``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`class_student_join` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\dakoii_org.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`dakoii_org``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`dakoii_org` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\dakoii_users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`dakoii_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`dakoii_users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\grades.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`grades``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`grades` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\schools.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`schools``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`schools` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\selection.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`selection``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`selection` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\settings.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`settings``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`settings` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\student_assessment_files.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`student_assessment_files``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`student_assessment_files` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\student_audit_trail.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`student_audit_trail``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`student_audit_trail` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\student_school_history.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`student_school_history``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`student_school_history` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\students.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`students``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`students` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\teachers.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`teachers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`teachers` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\smas_db\teachers_audit_trail.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``smas_db`.`teachers_audit_trail``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `smas_db`.`teachers_audit_trail` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\test_permissions_db\migrations.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``test_permissions_db`.`migrations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `test_permissions_db`.`migrations` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\test_permissions_db\permissions.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``test_permissions_db`.`permissions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `test_permissions_db`.`permissions` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\test_permissions_db\user_permissions.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``test_permissions_db`.`user_permissions``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `test_permissions_db`.`user_permissions` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\test_permissions_db\users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``test_permissions_db`.`users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `test_permissions_db`.`users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_activity.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_activity``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_activity` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_activity_meta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_activity_meta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_activity_meta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_invitations.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_invitations``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_invitations` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_notifications.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_notifications``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_notifications` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_notifications_meta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_notifications_meta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_notifications_meta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_optouts.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_optouts``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_optouts` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_user_blogs.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_user_blogs``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_user_blogs` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_user_blogs_blogmeta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_user_blogs_blogmeta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_user_blogs_blogmeta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_xprofile_data.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_xprofile_data``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_xprofile_data` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_xprofile_fields.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_xprofile_fields``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_xprofile_fields` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_xprofile_groups.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_xprofile_groups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_xprofile_groups` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_bp_xprofile_meta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_bp_xprofile_meta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_bp_xprofile_meta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_commentmeta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_commentmeta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_commentmeta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_comments.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_comments``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_comments` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_links.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_links``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_links` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_options.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_options``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_options` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_postmeta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_postmeta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_postmeta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_posts.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_posts``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_posts` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_rt_rtm_activity.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_rt_rtm_activity``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_rt_rtm_activity` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_rt_rtm_api.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_rt_rtm_api``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_rt_rtm_api` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_rt_rtm_media.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_rt_rtm_media``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_rt_rtm_media` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_rt_rtm_media_interaction.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_rt_rtm_media_interaction``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_rt_rtm_media_interaction` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_rt_rtm_media_meta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_rt_rtm_media_meta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_rt_rtm_media_meta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_signups.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_signups``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_signups` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_term_relationships.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_term_relationships``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_term_relationships` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_term_taxonomy.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_term_taxonomy``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_term_taxonomy` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_termmeta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_termmeta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_termmeta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_terms.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_terms``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_terms` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_um_metadata.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_um_metadata``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_um_metadata` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_usermeta.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_usermeta``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_usermeta` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_users.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_users``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_users` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_wpuf_subscribers.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_wpuf_subscribers``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_wpuf_subscribers` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 3 in a file operation.
2025-08-09  9:43:51 0 [ERROR] InnoDB: The error means the system cannot find the path specified.
2025-08-09  9:43:51 0 [ERROR] InnoDB: If you are installing InnoDB, remember that you must create directories yourself, InnoDB does not create them.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Cannot open datafile for read-only: '.\wptest_db\wp_wpuf_transaction.ibd' OS error: 203
2025-08-09  9:43:51 0 [ERROR] InnoDB: Operating system error number 203 in a file operation.
2025-08-09  9:43:51 0 [Note] InnoDB: Some operating system error numbers are described at https://mariadb.com/kb/en/library/operating-system-error-codes/
2025-08-09  9:43:51 0 [ERROR] InnoDB: Could not find a valid tablespace file for ``wptest_db`.`wp_wpuf_transaction``. Please refer to https://mariadb.com/kb/en/innodb-data-dictionary-troubleshooting/ for how to resolve the issue.
2025-08-09  9:43:51 0 [Warning] InnoDB: Ignoring tablespace for `wptest_db`.`wp_wpuf_transaction` because it could not be opened.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=326] log sequence number 831140 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=377] log sequence number 1150042 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=321] log sequence number 2690153 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=343] log sequence number 2766022 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=324] log sequence number 2632828 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=325] log sequence number 980613 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=331] log sequence number 2792810 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=305] log sequence number 515117 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09  9:43:51 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09  9:43:51 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09  9:43:51 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 09:43:51 0x21642025-08-09  9:43:51 0 [Note] InnoDB: 10.4.32 started; log sequence number 388620; transaction id 7596562645876110089
  InnoDB: Assertion failure in file D:\winx64-packages\build\src\storage\innobase\include\fut0lst.inl line 67
InnoDB: Failing assertion: addr.page == FIL_NULL || addr.boffset >= FIL_PAGE_DATA
InnoDB: We intentionally generate a memory trap.
InnoDB: Submit a detailed bug report to https://jira.mariadb.org/
InnoDB: If you get repeated assertion failures or crashes, even
InnoDB: immediately after the mysqld startup, there may be
InnoDB: corruption in the InnoDB tablespace. Please refer to
InnoDB: https://mariadb.com/kb/en/library/innodb-recovery-modes/
InnoDB: about forcing recovery.
2025-08-09  9:43:51 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-08-09  9:43:51 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=9] log seque250809  9:43:51 n[ERROR] mysqld got exception 0x80000003 ;
cSorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

eWe will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

 Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
nkey_buffer_size=16777216
uread_buffer_size=262144
mmax_used_connections=0
bmax_threads=65537
ethread_count=6
rIt is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
 Hope that's ok; if not, decrease some variables in the equation.

2Thread pointer: 0x1db7bb40e58
7Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
55791 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number=306] log sequence number 8026525 is in the future! Current system log sequence number 388629.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Your database may be corrupt or you may have copied the InnoDB tablespace but not the InnoDB log files. Please refer to https://mariadb.com/kb/en/library/innodb-recovery-modes/ for information about forcing recovery.
2025-08-09  9:43:51 0 [Note] Server socket created on IP: '::'.
2025-08-09  9:43:51 0 [ERROR] InnoDB: Page [page id: space=0, page number2025-08-09  9:45:30 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 20940
2025-08-09  9:45:30 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09  9:45:30 0 [Note] InnoDB: Uses event mutexes
2025-08-09  9:45:30 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-08-09  9:45:30 0 [Note] InnoDB: Number of pools: 1
2025-08-09  9:45:30 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09  9:45:30 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09  9:45:30 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09  9:45:30 0 [Note] InnoDB: The log sequence number 139827 in the system tablespace does not match the log sequence number 388620 in the ib_logfiles!
2025-08-09  9:45:30 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09  9:45:30 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09  9:45:30 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09  9:45:30 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09  9:45:30 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09  9:45:30 0 [Note] InnoDB: Waiting for purge to start
2025-08-09  9:45:31 0 [Note] InnoDB: 10.4.32 started; log sequence number 388620; transaction id 216
2025-08-09  9:45:31 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-08-09  9:45:31 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09  9:45:31 0 [Note] Server socket created on IP: '::'.
