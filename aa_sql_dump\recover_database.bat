@echo off
REM AGRISTATS_DB Recovery Script
REM Run this script to attempt database recovery

echo =====================================================
echo AGRISTATS_DB DATABASE RECOVERY SCRIPT
echo =====================================================
echo.
echo WARNING: This script will modify MySQL system files
echo Make sure you have backups before proceeding!
echo.
pause

echo Step 1: Stopping MySQL service...
net stop mysql
if %errorlevel% neq 0 (
    echo Failed to stop MySQL service. Please stop it manually.
    pause
)

echo Step 2: Backing up current ibdata1...
if exist ibdata1 (
    copy ibdata1 ibdata1_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%
    echo Backup created: ibdata1_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%
)

echo Step 3: Restoring ibdata1 from backup...
if exist ibdata1-B (
    copy ibdata1-B ibdata1
    echo ibdata1 restored from ibdata1-B
) else (
    echo ERROR: ibdata1-B file not found!
    echo Recovery cannot proceed.
    pause
    exit /b 1
)

echo Step 4: Starting MySQL service...
net start mysql
if %errorlevel% neq 0 (
    echo Failed to start MySQL service. Please start it manually.
    pause
)

echo Step 5: Waiting for MySQL to initialize...
timeout /t 10

echo Step 6: Checking database access...
mysql -u root -e "USE agristats_db; SHOW TABLES;" > table_check.txt 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Tables are now accessible!
    echo Creating full database dump...
    mysqldump -u root --single-transaction --routines --triggers agristats_db > aa_sql_dump\agristats_db_recovered_with_data.sql
    echo Full dump created: aa_sql_dump\agristats_db_recovered_with_data.sql
) else (
    echo Tables still not accessible. Attempting repair...
    mysql -u root -e "USE agristats_db; REPAIR TABLE adx_country;" 2>&1
    echo Check table_check.txt for details
)

echo.
echo Recovery process completed.
echo Check the results and verify table access.
pause
