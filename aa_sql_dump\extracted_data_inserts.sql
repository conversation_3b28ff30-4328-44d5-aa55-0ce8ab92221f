﻿-- =====================================================
-- EXTRACTED DATA FROM .IBD FILES
-- =====================================================

-- Country data extracted from adx_country.ibd
INSERT INTO `adx_country` (`id`, `name`, `code`) VALUES (1, 'Papua New Guinea', 'PG');
INSERT INTO `adx_country` (`id`, `name`, `code`) VALUES (2, 'Australia', 'AU');

-- Crop data extracted from adx_crops.ibd
INSERT INTO `adx_crops` (`id`, `name`) VALUES (1, 'Cocoa');
INSERT INTO `adx_crops` (`id`, `name`) VALUES (2, 'Coffee');
INSERT INTO `adx_crops` (`id`, `name`) VALUES (3, 'Rubber');

-- User data extracted from users.ibd (emails only)
INSERT INTO `users` (`id`, `username`, `email`) VALUES (1, 'DASatesta', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (2, 'Forllotesta', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (3, 'Flajoaitapeitu', '<EMAIL>');
INSERT INTO `users` (`id`, `username`, `email`) VALUES (4, 'Boxxanziinols', '<EMAIL>');

-- =====================================================
-- NOTE: Additional data may exist in other .ibd files
-- This represents only the data that could be extracted
-- using text pattern matching from binary files.
-- =====================================================
